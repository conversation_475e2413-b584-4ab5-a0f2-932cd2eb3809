import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosResponseWithHTTP2, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace managedidentities_v1alpha1 {
    export interface Options extends GlobalOptions {
        version: 'v1alpha1';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * Managed Service for Microsoft Active Directory API
     *
     * The Managed Service for Microsoft Active Directory API is used for managing a highly available, hardened service running Microsoft Active Directory (AD).
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const managedidentities = google.managedidentities('v1alpha1');
     * ```
     */
    export class Managedidentities {
        context: APIRequestContext;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    export interface Schema$AttachTrustRequest {
        /**
         * The domain trust resource.
         */
        trust?: Schema$Trust;
    }
    /**
     * Represents a Managed Microsoft Identities backup.
     */
    export interface Schema$Backup {
        /**
         * Output only. The time the backups was created.
         */
        createTime?: string | null;
        /**
         * Optional. A short description of the backup.
         */
        description?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The unique name of the Backup in the form of projects/{project_id\}/locations/global/domains/{domain_name\}/backups/{name\}
         */
        name?: string | null;
        /**
         * Output only. The current state of the backup.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the current status of this backup, if available.
         */
        statusMessage?: string | null;
        /**
         * Output only. Indicates whether it’s an on-demand backup or scheduled.
         */
        type?: string | null;
        /**
         * Output only. Last update time.
         */
        updateTime?: string | null;
    }
    /**
     * Associates `members`, or principals, with a `role`.
     */
    export interface Schema$Binding {
        /**
         * The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        condition?: Schema$Expr;
        /**
         * Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid\}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid\}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid\}.svc.id.goog[{namespace\}/{kubernetes-sa\}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid\}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain\}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/group/{group_id\}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/x`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/subject/{subject_attribute_value\}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/group/{group_id\}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/attribute.{attribute_name\}/{attribute_value\}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number\}/locations/global/workloadIdentityPools/{pool_id\}/x`: All identities in a workload identity pool. * `deleted:user:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid\}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid\}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid\}?uid={uniqueid\}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid\}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id\}/subject/{subject_attribute_value\}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.
         */
        members?: string[] | null;
        /**
         * Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).
         */
        role?: string | null;
    }
    /**
     * The request message for Operations.CancelOperation.
     */
    export interface Schema$CancelOperationRequest {
    }
    /**
     * Certificate used to configure LDAPS.
     */
    export interface Schema$Certificate {
        /**
         * The certificate expire time.
         */
        expireTime?: string | null;
        /**
         * The issuer of this certificate.
         */
        issuingCertificate?: Schema$Certificate;
        /**
         * The certificate subject.
         */
        subject?: string | null;
        /**
         * The additional hostnames for the domain.
         */
        subjectAlternativeName?: string[] | null;
        /**
         * The certificate thumbprint which uniquely identifies the certificate.
         */
        thumbprint?: string | null;
    }
    /**
     * CheckMigrationPermissionRequest is the request message for CheckMigrationPermission method.
     */
    export interface Schema$CheckMigrationPermissionRequest {
    }
    /**
     * CheckMigrationPermissionResponse is the response message for CheckMigrationPermission method.
     */
    export interface Schema$CheckMigrationPermissionResponse {
        /**
         * The state of SID filtering of all the domains which has trust established.
         */
        onpremDomains?: Schema$OnPremDomainSIDDetails[];
        /**
         * The state of DomainMigration.
         */
        state?: string | null;
    }
    /**
     * Time window specified for daily operations.
     */
    export interface Schema$DailyCycle {
        /**
         * Output only. Duration of the time window, set by service producer.
         */
        duration?: string | null;
        /**
         * Time within the day to start the operations.
         */
        startTime?: Schema$TimeOfDay;
    }
    /**
     * Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp
     */
    export interface Schema$Date {
        /**
         * Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.
         */
        day?: number | null;
        /**
         * Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.
         */
        month?: number | null;
        /**
         * Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.
         */
        year?: number | null;
    }
    /**
     * DenyMaintenancePeriod definition. Maintenance is forbidden within the deny period. The start_date must be less than the end_date.
     */
    export interface Schema$DenyMaintenancePeriod {
        /**
         * Deny period end date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be before the end.
         */
        endDate?: Schema$Date;
        /**
         * Deny period start date. This can be: * A full date, with non-zero year, month and day values. * A month and day value, with a zero year. Allows recurring deny periods each year. Date matching this period will have to be the same or after the start.
         */
        startDate?: Schema$Date;
        /**
         * Time in UTC when the Blackout period starts on start_date and ends on end_date. This can be: * Full time. * All zeros for 00:00:00 UTC
         */
        time?: Schema$TimeOfDay;
    }
    export interface Schema$DetachTrustRequest {
        /**
         * The domain trust resource to removed.
         */
        trust?: Schema$Trust;
    }
    /**
     * DisableMigrationRequest is the request message for DisableMigration method.
     */
    export interface Schema$DisableMigrationRequest {
    }
    /**
     * If the domain is being changed, it will be placed into the UPDATING state, which indicates that the resource is being reconciled. At this point, Get will reflect an intermediate state.
     */
    export interface Schema$Domain {
        /**
         * Optional. Configuration for audit logs. True if audit logs are enabled, else false. Default is audit logs disabled.
         */
        auditLogsEnabled?: boolean | null;
        /**
         * Optional. The full names of the Google Compute Engine [networks](/compute/docs/networks-and-firewalls#networks) to which the instance is connected. Network can be added using UpdateDomain later. Domain is only available on network part of authorized_networks. Caller needs to make sure that CIDR subnets do not overlap between networks, else domain creation will fail.
         */
        authorizedNetworks?: string[] | null;
        /**
         * Output only. The time the instance was created. Synthetic field is populated automatically by CCFE. go/ccfe-synthetic-field-user-guide
         */
        createTime?: string | null;
        /**
         * Output only. Fully-qualified domain name of the exposed domain used by clients to connect to the service. Similar to what would be chosen for an Active Directory that is set up on an internal network.
         */
        fqdn?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. Locations where domain needs to be provisioned. regions e.g. us-west1 or us-east4 Service supports up to 4 locations at once. Each location will use a /26 block.
         */
        locations?: string[] | null;
        /**
         * Optional. Name of customer-visible admin used to perform Active Directory operations. If not specified `setupadmin` would be used.
         */
        managedIdentitiesAdminName?: string | null;
        /**
         * Output only. Unique name of the domain in this scope including projects and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`.
         */
        name?: string | null;
        /**
         * Required. The CIDR range of internal addresses that are reserved for this domain. Reserved networks must be /24 or larger. Ranges must be unique and non-overlapping with existing subnets in [Domain].[authorized_networks].
         */
        reservedIpRange?: string | null;
        /**
         * Output only. The current state of this domain.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the current status of this domain, if available.
         */
        statusMessage?: string | null;
        /**
         * Output only. The current trusts associated with the domain.
         */
        trusts?: Schema$Trust[];
        /**
         * Output only. Last update time. Synthetic field is populated automatically by CCFE.
         */
        updateTime?: string | null;
    }
    /**
     * DomainJoinMachineRequest is the request message for DomainJoinMachine method
     */
    export interface Schema$DomainJoinMachineRequest {
        /**
         * Optional. force if True, forces domain join even if the computer account already exists.
         */
        force?: boolean | null;
        /**
         * Optional. OU name where the VM needs to be domain joined
         */
        ouName?: string | null;
        /**
         * Required. Full instance id token of compute engine VM to verify instance identity. More about this: https://cloud.google.com/compute/docs/instances/verifying-instance-identity#request_signature
         */
        vmIdToken?: string | null;
    }
    /**
     * DomainJoinMachineResponse is the response message for DomainJoinMachine method
     */
    export interface Schema$DomainJoinMachineResponse {
        /**
         * Offline domain join blob as the response
         */
        domainJoinBlob?: string | null;
    }
    /**
     * A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); \}
     */
    export interface Schema$Empty {
    }
    /**
     * EnableMigrationRequest is the request message for EnableMigration method.
     */
    export interface Schema$EnableMigrationRequest {
        /**
         * Optional. Period after which the migration would be auto disabled. If unspecified, a default timeout of 48h is used.
         */
        enableDuration?: string | null;
        /**
         * Required. List of the on-prem domains to be migrated.
         */
        migratingDomains?: Schema$OnPremDomainDetails[];
    }
    /**
     * Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: "Summary size limit" description: "Determines if a summary is less than 100 chars" expression: "document.summary.size() < 100" Example (Equality): title: "Requestor is owner" description: "Determines if requestor is the document owner" expression: "document.owner == request.auth.claims.email" Example (Logic): title: "Public documents" description: "Determine whether the document should be publicly visible" expression: "document.type != 'private' && document.type != 'internal'" Example (Data Manipulation): title: "Notification string" description: "Create a notification string with a timestamp." expression: "'New message received at ' + string(document.create_time)" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.
     */
    export interface Schema$Expr {
        /**
         * Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.
         */
        description?: string | null;
        /**
         * Textual representation of an expression in Common Expression Language syntax.
         */
        expression?: string | null;
        /**
         * Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.
         */
        location?: string | null;
        /**
         * Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.
         */
        title?: string | null;
    }
    /**
     * ExtendSchemaRequest is the request message for ExtendSchema method.
     */
    export interface Schema$ExtendSchemaRequest {
        /**
         * Required. Description for Schema Change.
         */
        description?: string | null;
        /**
         * File uploaded as a byte stream input.
         */
        fileContents?: string | null;
        /**
         * File stored in Cloud Storage bucket and represented in the form projects/{project_id\}/buckets/{bucket_name\}/objects/{object_name\} File should be in the same project as the domain.
         */
        gcsPath?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudManagedidentitiesV1alpha1OpMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudManagedidentitiesV1beta1OpMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$GoogleCloudManagedidentitiesV1OpMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
         */
        requestedCancellation?: boolean | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Instance represents the interface for SLM services to actuate the state of control plane resources. Example Instance in JSON, where consumer-project-number=123456, producer-project-id=cloud-sql: ```json Instance: { "name": "projects/123456/locations/us-east1/instances/prod-instance", "create_time": { "seconds": **********, \}, "labels": { "env": "prod", "foo": "bar" \}, "state": READY, "software_versions": { "software_update": "cloud-sql-09-28-2018", \}, "maintenance_policy_names": { "UpdatePolicy": "projects/123456/locations/us-east1/maintenancePolicies/prod-update-policy", \} "tenant_project_id": "cloud-sql-test-tenant", "producer_metadata": { "cloud-sql-tier": "basic", "cloud-sql-instance-size": "1G", \}, "provisioned_resources": [ { "resource-type": "compute-instance", "resource-url": "https://www.googleapis.com/compute/v1/projects/cloud-sql/zones/us-east1-b/instances/vm-1", \} ], "maintenance_schedules": { "csa_rollout": { "start_time": { "seconds": **********, \}, "end_time": { "seconds": **********, \}, \}, "ncsa_rollout": { "start_time": { "seconds": **********, \}, "end_time": { "seconds": **********, \}, \} \}, "consumer_defined_name": "my-sql-instance1", \} ``` LINT.IfChange
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1Instance {
        /**
         * consumer_defined_name is the name of the instance set by the service consumers. Generally this is different from the `name` field which reperesents the system-assigned id of the instance which the service consumers do not recognize. This is a required field for tenants onboarding to Maintenance Window notifications (go/slm-rollout-maintenance-policies#prerequisites).
         */
        consumerDefinedName?: string | null;
        /**
         * Output only. Timestamp when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. The instance_type of this instance of format: projects/{project_number\}/locations/{location_id\}/instanceTypes/{instance_type_id\}. Instance Type represents a high-level tier or SKU of the service that this instance belong to. When enabled(eg: Maintenance Rollout), Rollout uses 'instance_type' along with 'software_versions' to determine whether instance needs an update or not.
         */
        instanceType?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the referenced policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_settings.maintenance_policies is set.
         */
        maintenancePolicyNames?: {
            [key: string]: string;
        } | null;
        /**
         * The MaintenanceSchedule contains the scheduling information of published maintenance schedule with same key as software_versions.
         */
        maintenanceSchedules?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule;
        } | null;
        /**
         * Optional. The MaintenanceSettings associated with instance.
         */
        maintenanceSettings?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings;
        /**
         * Unique name of the resource. It uses the form: `projects/{project_number\}/locations/{location_id\}/instances/{instance_id\}` Note: This name is passed, stored and logged across the rollout system. So use of consumer project_id or any other consumer PII in the name is strongly discouraged for wipeout (go/wipeout) compliance. See go/elysium/project_ids#storage-guidance for more details.
         */
        name?: string | null;
        /**
         * Optional. notification_parameter are information that service producers may like to include that is not relevant to Rollout. This parameter will only be passed to Gamma and Cloud Logging for notification/logging purpose.
         */
        notificationParameters?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter;
        } | null;
        /**
         * Output only. Custom string attributes used primarily to expose producer-specific information in monitoring dashboards. See go/get-instance-metadata.
         */
        producerMetadata?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. The list of data plane resources provisioned for this instance, e.g. compute VMs. See go/get-instance-metadata.
         */
        provisionedResources?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource[];
        /**
         * Link to the SLM instance template. Only populated when updating SLM instances via SSA's Actuation service adaptor. Service producers with custom control plane (e.g. Cloud SQL) doesn't need to populate this field. Instead they should use software_versions.
         */
        slmInstanceTemplate?: string | null;
        /**
         * Output only. SLO metadata for instance classification in the Standardized dataplane SLO platform. See go/cloud-ssa-standard-slo for feature description.
         */
        sloMetadata?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata;
        /**
         * Software versions that are used to deploy this instance. This can be mutated by rollout services.
         */
        softwareVersions?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Current lifecycle state of the resource (e.g. if it's being created or ready to use).
         */
        state?: string | null;
        /**
         * Output only. ID of the associated GCP tenant project. See go/get-instance-metadata.
         */
        tenantProjectId?: string | null;
        /**
         * Output only. Timestamp when the resource was last modified.
         */
        updateTime?: string | null;
    }
    /**
     * Maintenance schedule which is exposed to customer and potentially end user, indicating published upcoming future maintenance schedule
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSchedule {
        /**
         * This field is deprecated, and will be always set to true since reschedule can happen multiple times now. This field should not be removed until all service producers remove this for their customers.
         */
        canReschedule?: boolean | null;
        /**
         * The scheduled end time for the maintenance.
         */
        endTime?: string | null;
        /**
         * The rollout management policy this maintenance schedule is associated with. When doing reschedule update request, the reschedule should be against this given policy.
         */
        rolloutManagementPolicy?: string | null;
        /**
         * schedule_deadline_time is the time deadline any schedule start time cannot go beyond, including reschedule. It's normally the initial schedule start time plus maintenance window length (1 day or 1 week). Maintenance cannot be scheduled to start beyond this deadline.
         */
        scheduleDeadlineTime?: string | null;
        /**
         * The scheduled start time for the maintenance.
         */
        startTime?: string | null;
    }
    /**
     * Maintenance settings associated with instance. Allows service producers and end users to assign settings that controls maintenance on this instance.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1MaintenanceSettings {
        /**
         * Optional. Exclude instance from maintenance. When true, rollout service will not attempt maintenance on the instance. Rollout service will include the instance in reported rollout progress as not attempted.
         */
        exclude?: boolean | null;
        /**
         * Optional. If the update call is triggered from rollback, set the value as true.
         */
        isRollback?: boolean | null;
        /**
         * Optional. The MaintenancePolicies that have been attached to the instance. The key must be of the type name of the oneof policy name defined in MaintenancePolicy, and the embedded policy must define the same policy type. For details, please refer to go/mr-user-guide. Should not be set if maintenance_policy_names is set. If only the name is needed, then only populate MaintenancePolicy.name.
         */
        maintenancePolicies?: {
            [key: string]: Schema$MaintenancePolicy;
        } | null;
    }
    /**
     * Node information for custom per-node SLO implementations. SSA does not support per-node SLO, but producers can populate per-node information in SloMetadata for custom precomputations. SSA Eligibility Exporter will emit per-node metric based on this information.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata {
        /**
         * The location of the node, if different from instance location.
         */
        location?: string | null;
        /**
         * The id of the node. This should be equal to SaasInstanceNode.node_id.
         */
        nodeId?: string | null;
        /**
         * If present, this will override eligibility for the node coming from instance or exclusions for specified SLIs.
         */
        perSliEligibility?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility;
    }
    /**
     * Contains notification related data.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1NotificationParameter {
        /**
         * Optional. Array of string values. e.g. instance's replica information.
         */
        values?: string[] | null;
    }
    /**
     * PerSliSloEligibility is a mapping from an SLI name to eligibility.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility {
        /**
         * An entry in the eligibilities map specifies an eligibility for a particular SLI for the given instance. The SLI key in the name must be a valid SLI name specified in the Eligibility Exporter binary flags otherwise an error will be emitted by Eligibility Exporter and the oncaller will be alerted. If an SLI has been defined in the binary flags but the eligibilities map does not contain it, the corresponding SLI time series will not be emitted by the Eligibility Exporter. This ensures a smooth rollout and compatibility between the data produced by different versions of the Eligibility Exporters. If eligibilities map contains a key for an SLI which has not been declared in the binary flags, there will be an error message emitted in the Eligibility Exporter log and the metric for the SLI in question will not be emitted.
         */
        eligibilities?: {
            [key: string]: Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility;
        } | null;
    }
    /**
     * Describes provisioned dataplane resources.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1ProvisionedResource {
        /**
         * Type of the resource. This can be either a GCP resource or a custom one (e.g. another cloud provider's VM). For GCP compute resources use singular form of the names listed in GCP compute API documentation (https://cloud.google.com/compute/docs/reference/rest/v1/), prefixed with 'compute-', for example: 'compute-instance', 'compute-disk', 'compute-autoscaler'.
         */
        resourceType?: string | null;
        /**
         * URL identifying the resource, e.g. "https://www.googleapis.com/compute/v1/projects/...)".
         */
        resourceUrl?: string | null;
    }
    /**
     * SloEligibility is a tuple containing eligibility value: true if an instance is eligible for SLO calculation or false if it should be excluded from all SLO-related calculations along with a user-defined reason.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloEligibility {
        /**
         * Whether an instance is eligible or ineligible.
         */
        eligible?: boolean | null;
        /**
         * User-defined reason for the current value of instance eligibility. Usually, this can be directly mapped to the internal state. An empty reason is allowed.
         */
        reason?: string | null;
    }
    /**
     * SloMetadata contains resources required for proper SLO classification of the instance.
     */
    export interface Schema$GoogleCloudSaasacceleratorManagementProvidersV1SloMetadata {
        /**
         * Optional. List of nodes. Some producers need to use per-node metadata to calculate SLO. This field allows such producers to publish per-node SLO meta data, which will be consumed by SSA Eligibility Exporter and published in the form of per node metric to Monarch.
         */
        nodes?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1NodeSloMetadata[];
        /**
         * Optional. Multiple per-instance SLI eligibilities which apply for individual SLIs.
         */
        perSliEligibility?: Schema$GoogleCloudSaasacceleratorManagementProvidersV1PerSliSloEligibility;
        /**
         * Name of the SLO tier the Instance belongs to. This name will be expected to match the tiers specified in the service SLO configuration. Field is mandatory and must not be empty.
         */
        tier?: string | null;
    }
    /**
     * LDAPSSettings represents the ldaps settings for domain resource. LDAP is the Lightweight Directory Access Protocol, defined in https://tools.ietf.org/html/rfc4511. The settings object configures LDAP over SSL/TLS, whether it is over port 636 or the StartTLS operation. If LDAPSSettings is being changed, it will be placed into the UPDATING state, which indicates that the resource is being reconciled. At this point, Get will reflect an intermediate state.
     */
    export interface Schema$LDAPSSettings {
        /**
         * Output only. The certificate used to configure LDAPS. Certificates can be chained with a maximum length of 15.
         */
        certificate?: Schema$Certificate;
        /**
         * Input only. The password used to encrypt the uploaded pfx certificate.
         */
        certificatePassword?: string | null;
        /**
         * Input only. The uploaded PKCS12-formatted certificate to configure LDAPS with. It will enable the domain controllers in this domain to accept LDAPS connections (either LDAP over SSL/TLS or the StartTLS operation). A valid certificate chain must form a valid x.509 certificate chain (or be comprised of a single self-signed certificate. It must be encrypted with either: 1) PBES2 + PBKDF2 + AES256 encryption and SHA256 PRF; or 2) pbeWithSHA1And3-KeyTripleDES-CBC Private key must be included for the leaf / single self-signed certificate. Note: For a fqdn your-example-domain.com, the wildcard fqdn is *.your-example-domain.com. Specifically the leaf certificate must have: - Either a blank subject or a subject with CN matching the wildcard fqdn. - Exactly two SANs - the fqdn and wildcard fqdn. - Encipherment and digital key signature key usages. - Server authentication extended key usage (OID=*******.*******.1) - Private key must be in one of the following formats: RSA, ECDSA, ED25519. - Private key must have appropriate key length: 2048 for RSA, 256 for ECDSA - Signature algorithm of the leaf certificate cannot be MD2, MD5 or SHA1.
         */
        certificatePfx?: string | null;
        /**
         * The resource name of the LDAPS settings. Uses the form: `projects/{project\}/locations/{location\}/domains/{domain\}`.
         */
        name?: string | null;
        /**
         * Output only. The current state of this LDAPS settings.
         */
        state?: string | null;
        /**
         * Output only. Last update time.
         */
        updateTime?: string | null;
    }
    /**
     * ListBackupsResponse is the response message for ListBackups method.
     */
    export interface Schema$ListBackupsResponse {
        /**
         * A list of Cloud AD backups in the domain.
         */
        backups?: Schema$Backup[];
        /**
         * Token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    export interface Schema$ListDomainsResponse {
        /**
         * A list of Managed Identities Service domains in the project.
         */
        domains?: Schema$Domain[];
        /**
         * Token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * The response message for Locations.ListLocations.
     */
    export interface Schema$ListLocationsResponse {
        /**
         * A list of locations that matches the specified filter in the request.
         */
        locations?: Schema$Location[];
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
    }
    /**
     * The response message for Operations.ListOperations.
     */
    export interface Schema$ListOperationsResponse {
        /**
         * The standard List next-page token.
         */
        nextPageToken?: string | null;
        /**
         * A list of operations that matches the specified filter in the request.
         */
        operations?: Schema$Operation[];
    }
    /**
     * ListPeeringsResponse is the response message for ListPeerings method.
     */
    export interface Schema$ListPeeringsResponse {
        /**
         * Token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of Managed Identities Service Peerings in the project.
         */
        peerings?: Schema$Peering[];
        /**
         * Locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * ListSQLIntegrationsResponse is the response message for ListSQLIntegrations method.
     */
    export interface Schema$ListSQLIntegrationsResponse {
        /**
         * Token to retrieve the next page of results, or empty if there are no more results in the list.
         */
        nextPageToken?: string | null;
        /**
         * A list of SQLIntegrations of a domain.
         */
        sqlIntegrations?: Schema$SQLIntegration[];
        /**
         * A list of locations that could not be reached.
         */
        unreachable?: string[] | null;
    }
    /**
     * A resource that represents a Google Cloud location.
     */
    export interface Schema$Location {
        /**
         * The friendly name for this location, typically a nearby city name. For example, "Tokyo".
         */
        displayName?: string | null;
        /**
         * Cross-service attributes for the location. For example {"cloud.googleapis.com/region": "us-east1"\}
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * The canonical id for this location. For example: `"us-east1"`.
         */
        locationId?: string | null;
        /**
         * Service-specific metadata. For example the available capacity at the given location.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * Resource name for the location, which may vary between implementations. For example: `"projects/example-project/locations/us-east1"`
         */
        name?: string | null;
    }
    /**
     * Defines policies to service maintenance events.
     */
    export interface Schema$MaintenancePolicy {
        /**
         * Output only. The time when the resource was created.
         */
        createTime?: string | null;
        /**
         * Optional. Description of what this policy is for. Create/Update methods return INVALID_ARGUMENT if the length is greater than 512.
         */
        description?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata. Each label is a key-value pair, where both the key and the value are arbitrary strings provided by the user.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Required. MaintenancePolicy name using the form: `projects/{project_id\}/locations/{location_id\}/maintenancePolicies/{maintenance_policy_id\}` where {project_id\} refers to a GCP consumer project ID, {location_id\} refers to a GCP region/zone, {maintenance_policy_id\} must be 1-63 characters long and match the regular expression `[a-z0-9]([-a-z0-9]*[a-z0-9])?`.
         */
        name?: string | null;
        /**
         * Optional. The state of the policy.
         */
        state?: string | null;
        /**
         * Maintenance policy applicable to instance update.
         */
        updatePolicy?: Schema$UpdatePolicy;
        /**
         * Output only. The time when the resource was updated.
         */
        updateTime?: string | null;
    }
    /**
     * MaintenanceWindow definition.
     */
    export interface Schema$MaintenanceWindow {
        /**
         * Daily cycle.
         */
        dailyCycle?: Schema$DailyCycle;
        /**
         * Weekly cycle.
         */
        weeklyCycle?: Schema$WeeklyCycle;
    }
    /**
     * OnPremDomainDetails is the message which contains details of on-prem domain which is trusted and needs to be migrated.
     */
    export interface Schema$OnPremDomainDetails {
        /**
         * Optional. Option to disable SID filtering.
         */
        disableSidFiltering?: boolean | null;
        /**
         * Required. FQDN of the on-prem domain being migrated.
         */
        domainName?: string | null;
    }
    /**
     * OnPremDomainDetails is the message which contains details of on-prem domain which is trusted and needs to be migrated.
     */
    export interface Schema$OnPremDomainSIDDetails {
        /**
         * FQDN of the on-prem domain being migrated.
         */
        name?: string | null;
        /**
         * Current SID filtering state.
         */
        sidFilteringState?: string | null;
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * Represents the metadata of the long-running operation.
     */
    export interface Schema$OperationMetadata {
        /**
         * Output only. API version used to start the operation.
         */
        apiVersion?: string | null;
        /**
         * Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         */
        cancelRequested?: boolean | null;
        /**
         * Output only. The time the operation was created.
         */
        createTime?: string | null;
        /**
         * Output only. The time the operation finished running.
         */
        endTime?: string | null;
        /**
         * Output only. Human-readable status of the operation, if any.
         */
        statusDetail?: string | null;
        /**
         * Output only. Server-defined resource path for the target of the operation.
         */
        target?: string | null;
        /**
         * Output only. Name of the verb executed by the operation.
         */
        verb?: string | null;
    }
    /**
     * Represents a Managed Microsoft Identities Peering.
     */
    export interface Schema$Peering {
        /**
         * Required. The full names of the Google Compute Engine [networks](/compute/docs/networks-and-firewalls#networks) to which the instance is connected. Caller needs to make sure that CIDR subnets do not overlap between networks, else peering creation will fail.
         */
        authorizedNetwork?: string | null;
        /**
         * Output only. The time the instance was created.
         */
        createTime?: string | null;
        /**
         * Required. Full domain resource path for the Managed AD Domain involved in peering. The resource path should be in the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        domainResource?: string | null;
        /**
         * Optional. Resource labels to represent user provided metadata.
         */
        labels?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. Unique name of the peering in this scope including projects and location using the form: `projects/{project_id\}/locations/global/peerings/{peering_id\}`.
         */
        name?: string | null;
        /**
         * Output only. The current state of this Peering.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the current status of this peering, if available.
         */
        statusMessage?: string | null;
        /**
         * Output only. Last update time.
         */
        updateTime?: string | null;
    }
    /**
     * An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { "bindings": [ { "role": "roles/resourcemanager.organizationAdmin", "members": [ "user:<EMAIL>", "group:<EMAIL>", "domain:google.com", "serviceAccount:<EMAIL>" ] \}, { "role": "roles/resourcemanager.organizationViewer", "members": [ "user:<EMAIL>" ], "condition": { "title": "expirable access", "description": "Does not grant access after Sep 2020", "expression": "request.time < timestamp('2020-10-01T00:00:00.000Z')", \} \} ], "etag": "BwWWja0YfJA=", "version": 3 \} ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).
     */
    export interface Schema$Policy {
        /**
         * Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.
         */
        bindings?: Schema$Binding[];
        /**
         * `etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.
         */
        etag?: string | null;
        /**
         * Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        version?: number | null;
    }
    export interface Schema$ReconfigureTrustRequest {
        /**
         * The domain trust resource with updated dns conditional forwarder.
         */
        trust?: Schema$Trust;
    }
    export interface Schema$ResetAdminPasswordRequest {
    }
    export interface Schema$ResetAdminPasswordResponse {
        /**
         * A random password. See admin for more information.
         */
        password?: string | null;
    }
    /**
     * RestoreDomainRequest is the request received by RestoreDomain rpc
     */
    export interface Schema$RestoreDomainRequest {
        /**
         * Required. ID of the backup to be restored
         */
        backupId?: string | null;
    }
    /**
     * Configure the schedule.
     */
    export interface Schema$Schedule {
        /**
         * Allows to define schedule that runs specified day of the week.
         */
        day?: string | null;
        /**
         * Output only. Duration of the time window, set by service producer.
         */
        duration?: string | null;
        /**
         * Time within the window to start the operations.
         */
        startTime?: Schema$TimeOfDay;
    }
    /**
     * Request message for `SetIamPolicy` method.
     */
    export interface Schema$SetIamPolicyRequest {
        /**
         * REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.
         */
        policy?: Schema$Policy;
    }
    /**
     * Represents the SQL instance integrated with AD.
     */
    export interface Schema$SQLIntegration {
        /**
         * Output only. The time the instance was created.
         */
        createTime?: string | null;
        /**
         * The unique name of the sql integration in the form of `projects/{project_id\}/locations/global/domains/{domain_name\}/sqlIntegrations/{sql_integration\}`
         */
        name?: string | null;
        /**
         * The full resource name of an integrated sql instance
         */
        sqlInstance?: string | null;
        /**
         * Output only. The current state of the managed OU.
         */
        state?: string | null;
        /**
         * Output only. Last update time for this SQL instance.
         */
        updateTime?: string | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Request message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsRequest {
        /**
         * The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).
         */
        permissions?: string[] | null;
    }
    /**
     * Response message for `TestIamPermissions` method.
     */
    export interface Schema$TestIamPermissionsResponse {
        /**
         * A subset of `TestPermissionsRequest.permissions` that the caller is allowed.
         */
        permissions?: string[] | null;
    }
    /**
     * Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.
     */
    export interface Schema$TimeOfDay {
        /**
         * Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value "24:00:00" for scenarios like business closing time.
         */
        hours?: number | null;
        /**
         * Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.
         */
        minutes?: number | null;
        /**
         * Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.
         */
        nanos?: number | null;
        /**
         * Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.
         */
        seconds?: number | null;
    }
    /**
     * Represents a relationship between two domains which makes it possible for users in one domain to be authenticated by a dc in another domain. Refer https://docs.microsoft.com/en-us/previous-versions/windows/it-pro/windows-server-2008-R2-and-2008/cc731335(v%3dws.10) If the trust is being changed, it will be placed into the UPDATING state, which indicates that the resource is being reconciled. At this point, Get will reflect an intermediate state.
     */
    export interface Schema$Trust {
        /**
         * Output only. The time the instance was created.
         */
        createTime?: string | null;
        /**
         * Output only. The last heartbeat time when the trust was known to be connected.
         */
        lastKnownTrustConnectedHeartbeatTime?: string | null;
        /**
         * The trust authentication type which decides whether the trusted side has forest/domain wide access or selective access to approved set of resources.
         */
        selectiveAuthentication?: boolean | null;
        /**
         * Output only. The current state of this trust.
         */
        state?: string | null;
        /**
         * Output only. Additional information about the current state of this trust, if available.
         */
        stateDescription?: string | null;
        /**
         * The target dns server ip addresses which can resolve the remote domain involved in trust.
         */
        targetDnsIpAddresses?: string[] | null;
        /**
         * The fully qualified target domain name which will be in trust with current domain.
         */
        targetDomainName?: string | null;
        /**
         * The trust direction decides the current domain is trusted, trusting or both.
         */
        trustDirection?: string | null;
        /**
         * Input only, and will not be stored. The trust secret used for handshake with target domain.
         */
        trustHandshakeSecret?: string | null;
        /**
         * The type of trust represented by the trust resource.
         */
        trustType?: string | null;
        /**
         * Output only. Last update time.
         */
        updateTime?: string | null;
    }
    /**
     * Maintenance policy applicable to instance updates.
     */
    export interface Schema$UpdatePolicy {
        /**
         * Optional. Relative scheduling channel applied to resource.
         */
        channel?: string | null;
        /**
         * Deny Maintenance Period that is applied to resource to indicate when maintenance is forbidden. The protocol supports zero-to-many such periods, but the current SLM Rollout implementation only supports zero-to-one.
         */
        denyMaintenancePeriods?: Schema$DenyMaintenancePeriod[];
        /**
         * Optional. Maintenance window that is applied to resources covered by this policy.
         */
        window?: Schema$MaintenanceWindow;
    }
    export interface Schema$ValidateTrustRequest {
        /**
         * The domain trust to validate trust state for.
         */
        trust?: Schema$Trust;
    }
    /**
     * Time window specified for weekly operations.
     */
    export interface Schema$WeeklyCycle {
        /**
         * User can specify multiple windows in a week. Minimum of 1 window.
         */
        schedule?: Schema$Schedule[];
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        global: Resource$Projects$Locations$Global;
        constructor(context: APIRequestContext);
        /**
         * Gets information about a location.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Location>>;
        get(params: Params$Resource$Projects$Locations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Get, options: MethodOptions | BodyResponseCallback<Schema$Location>, callback: BodyResponseCallback<Schema$Location>): void;
        get(params: Params$Resource$Projects$Locations$Get, callback: BodyResponseCallback<Schema$Location>): void;
        get(callback: BodyResponseCallback<Schema$Location>): void;
        /**
         * Lists information about the supported locations for this service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListLocationsResponse>>;
        list(params: Params$Resource$Projects$Locations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$List, options: MethodOptions | BodyResponseCallback<Schema$ListLocationsResponse>, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$List, callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListLocationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Get extends StandardParameters {
        /**
         * Resource name for the location.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$List extends StandardParameters {
        /**
         * A filter to narrow down results to a preferred subset. The filtering language accepts strings like `"displayName=tokyo"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).
         */
        filter?: string;
        /**
         * The resource that owns the locations collection, if applicable.
         */
        name?: string;
        /**
         * The maximum number of results to return. If not set, the service selects a default.
         */
        pageSize?: number;
        /**
         * A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Global {
        context: APIRequestContext;
        domains: Resource$Projects$Locations$Global$Domains;
        operations: Resource$Projects$Locations$Global$Operations;
        peerings: Resource$Projects$Locations$Global$Peerings;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Global$Domains {
        context: APIRequestContext;
        backups: Resource$Projects$Locations$Global$Domains$Backups;
        sqlIntegrations: Resource$Projects$Locations$Global$Domains$Sqlintegrations;
        constructor(context: APIRequestContext);
        /**
         * Adds AD trust in a given domain. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        attachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Attachtrust, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        attachTrust(params?: Params$Resource$Projects$Locations$Global$Domains$Attachtrust, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        attachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Attachtrust, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        attachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Attachtrust, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        attachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Attachtrust, callback: BodyResponseCallback<Schema$Operation>): void;
        attachTrust(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * AuditMigration API gets the current state of DomainMigration
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        checkMigrationPermission(params: Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        checkMigrationPermission(params?: Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$CheckMigrationPermissionResponse>>;
        checkMigrationPermission(params: Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        checkMigrationPermission(params: Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission, options: MethodOptions | BodyResponseCallback<Schema$CheckMigrationPermissionResponse>, callback: BodyResponseCallback<Schema$CheckMigrationPermissionResponse>): void;
        checkMigrationPermission(params: Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission, callback: BodyResponseCallback<Schema$CheckMigrationPermissionResponse>): void;
        checkMigrationPermission(callback: BodyResponseCallback<Schema$CheckMigrationPermissionResponse>): void;
        /**
         * Creates a Microsoft AD Domain in a given project. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Global$Domains$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Global$Domains$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes identified domain. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Global$Domains$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Removes identified trust. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        detachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Detachtrust, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        detachTrust(params?: Params$Resource$Projects$Locations$Global$Domains$Detachtrust, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        detachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Detachtrust, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        detachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Detachtrust, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        detachTrust(params: Params$Resource$Projects$Locations$Global$Domains$Detachtrust, callback: BodyResponseCallback<Schema$Operation>): void;
        detachTrust(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Disable Domain Migration
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        disableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Disablemigration, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        disableMigration(params?: Params$Resource$Projects$Locations$Global$Domains$Disablemigration, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        disableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Disablemigration, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        disableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Disablemigration, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        disableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Disablemigration, callback: BodyResponseCallback<Schema$Operation>): void;
        disableMigration(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * DomainJoinMachine API joins a Compute Engine VM to the domain
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        domainJoinMachine(params: Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        domainJoinMachine(params?: Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$DomainJoinMachineResponse>>;
        domainJoinMachine(params: Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        domainJoinMachine(params: Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine, options: MethodOptions | BodyResponseCallback<Schema$DomainJoinMachineResponse>, callback: BodyResponseCallback<Schema$DomainJoinMachineResponse>): void;
        domainJoinMachine(params: Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine, callback: BodyResponseCallback<Schema$DomainJoinMachineResponse>): void;
        domainJoinMachine(callback: BodyResponseCallback<Schema$DomainJoinMachineResponse>): void;
        /**
         * Enable Domain Migration
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        enableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Enablemigration, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        enableMigration(params?: Params$Resource$Projects$Locations$Global$Domains$Enablemigration, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        enableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Enablemigration, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        enableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Enablemigration, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        enableMigration(params: Params$Resource$Projects$Locations$Global$Domains$Enablemigration, callback: BodyResponseCallback<Schema$Operation>): void;
        enableMigration(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Extend Schema for Domain
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        extendSchema(params: Params$Resource$Projects$Locations$Global$Domains$Extendschema, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        extendSchema(params?: Params$Resource$Projects$Locations$Global$Domains$Extendschema, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        extendSchema(params: Params$Resource$Projects$Locations$Global$Domains$Extendschema, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        extendSchema(params: Params$Resource$Projects$Locations$Global$Domains$Extendschema, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        extendSchema(params: Params$Resource$Projects$Locations$Global$Domains$Extendschema, callback: BodyResponseCallback<Schema$Operation>): void;
        extendSchema(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Domain.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Global$Domains$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Global$Domains$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Domain>>;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Get, options: MethodOptions | BodyResponseCallback<Schema$Domain>, callback: BodyResponseCallback<Schema$Domain>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Get, callback: BodyResponseCallback<Schema$Domain>): void;
        get(callback: BodyResponseCallback<Schema$Domain>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Global$Domains$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Gets the domain ldaps settings.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Getldapssettings, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getLdapssettings(params?: Params$Resource$Projects$Locations$Global$Domains$Getldapssettings, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$LDAPSSettings>>;
        getLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Getldapssettings, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Getldapssettings, options: MethodOptions | BodyResponseCallback<Schema$LDAPSSettings>, callback: BodyResponseCallback<Schema$LDAPSSettings>): void;
        getLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Getldapssettings, callback: BodyResponseCallback<Schema$LDAPSSettings>): void;
        getLdapssettings(callback: BodyResponseCallback<Schema$LDAPSSettings>): void;
        /**
         * Lists Domains in a given project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Global$Domains$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Global$Domains$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListDomainsResponse>>;
        list(params: Params$Resource$Projects$Locations$Global$Domains$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$List, options: MethodOptions | BodyResponseCallback<Schema$ListDomainsResponse>, callback: BodyResponseCallback<Schema$ListDomainsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$List, callback: BodyResponseCallback<Schema$ListDomainsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListDomainsResponse>): void;
        /**
         * Updates the metadata and configuration of a specified domain. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Global$Domains$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Updates the dns conditional forwarder. Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        reconfigureTrust(params: Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        reconfigureTrust(params?: Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        reconfigureTrust(params: Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        reconfigureTrust(params: Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        reconfigureTrust(params: Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust, callback: BodyResponseCallback<Schema$Operation>): void;
        reconfigureTrust(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Resets managed identities admin password identified by managed_identities_admin_name
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        resetAdminPassword(params: Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        resetAdminPassword(params?: Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ResetAdminPasswordResponse>>;
        resetAdminPassword(params: Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        resetAdminPassword(params: Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword, options: MethodOptions | BodyResponseCallback<Schema$ResetAdminPasswordResponse>, callback: BodyResponseCallback<Schema$ResetAdminPasswordResponse>): void;
        resetAdminPassword(params: Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword, callback: BodyResponseCallback<Schema$ResetAdminPasswordResponse>): void;
        resetAdminPassword(callback: BodyResponseCallback<Schema$ResetAdminPasswordResponse>): void;
        /**
         * RestoreDomain restores domain backup mentioned in the RestoreDomainRequest
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        restore(params: Params$Resource$Projects$Locations$Global$Domains$Restore, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        restore(params?: Params$Resource$Projects$Locations$Global$Domains$Restore, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        restore(params: Params$Resource$Projects$Locations$Global$Domains$Restore, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        restore(params: Params$Resource$Projects$Locations$Global$Domains$Restore, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(params: Params$Resource$Projects$Locations$Global$Domains$Restore, callback: BodyResponseCallback<Schema$Operation>): void;
        restore(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Global$Domains$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Global$Domains$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        /**
         * Patches a single ldaps settings.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        updateLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        updateLdapssettings(params?: Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        updateLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        updateLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        updateLdapssettings(params: Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings, callback: BodyResponseCallback<Schema$Operation>): void;
        updateLdapssettings(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Validate the trust state Operation
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        validateTrust(params: Params$Resource$Projects$Locations$Global$Domains$Validatetrust, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        validateTrust(params?: Params$Resource$Projects$Locations$Global$Domains$Validatetrust, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        validateTrust(params: Params$Resource$Projects$Locations$Global$Domains$Validatetrust, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        validateTrust(params: Params$Resource$Projects$Locations$Global$Domains$Validatetrust, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        validateTrust(params: Params$Resource$Projects$Locations$Global$Domains$Validatetrust, callback: BodyResponseCallback<Schema$Operation>): void;
        validateTrust(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Attachtrust extends StandardParameters {
        /**
         * The resource domain name, project name and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$AttachTrustRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Checkmigrationpermission extends StandardParameters {
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        domain?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CheckMigrationPermissionRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Create extends StandardParameters {
        /**
         * The fully qualified domain name. e.g. mydomain.myorganization.com, with the following restrictions: * Must contain only lowercase letters, numbers, periods and hyphens. * Must start with a letter. * Must contain between 2-64 characters. * Must end with a number or a letter. * Must not start with period. * Must be unique within the project. * First segment length (mydomain form example above) shouldn't exceed 15 chars. * The last segment cannot be fully numeric.
         */
        domainName?: string;
        /**
         * Resource project name and location using the form: `projects/{project_id\}/locations/global`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Domain;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Delete extends StandardParameters {
        /**
         * Domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Detachtrust extends StandardParameters {
        /**
         * The resource domain name, project name, and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DetachTrustRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Disablemigration extends StandardParameters {
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        domain?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DisableMigrationRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Domainjoinmachine extends StandardParameters {
        /**
         * Required. The domain resource name using the form: projects/{project_id\}/locations/global/domains/{domain_name\}
         */
        domain?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$DomainJoinMachineRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Enablemigration extends StandardParameters {
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        domain?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$EnableMigrationRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Extendschema extends StandardParameters {
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        domain?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ExtendSchemaRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Get extends StandardParameters {
        /**
         * Domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Getldapssettings extends StandardParameters {
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$List extends StandardParameters {
        /**
         * Optional. Filter specifying constraints of a list operation. For example, `Domain.fqdn="mydomain.myorginization"`.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results following syntax at https://cloud.google.com/apis/design/design_patterns#sorting_order.
         */
        orderBy?: string;
        /**
         * If not specified, a default value of 1000 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the domain location using the form: `projects/{project_id\}/locations/global`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Patch extends StandardParameters {
        /**
         * Output only. Unique name of the domain in this scope including projects and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`.
         */
        name?: string;
        /**
         * Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from Domain: * `labels` * `locations` * `authorized_networks` * `audit_logs_enabled`
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Domain;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Reconfiguretrust extends StandardParameters {
        /**
         * The resource domain name, project name and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ReconfigureTrustRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Resetadminpassword extends StandardParameters {
        /**
         * The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ResetAdminPasswordRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Restore extends StandardParameters {
        /**
         * Required. resource name for the domain to which the backup belongs
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$RestoreDomainRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Updateldapssettings extends StandardParameters {
        /**
         * The resource name of the LDAPS settings. Uses the form: `projects/{project\}/locations/{location\}/domains/{domain\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. For the `FieldMask` definition, see https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#fieldmask
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$LDAPSSettings;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Validatetrust extends StandardParameters {
        /**
         * The resource domain name, project name, and location using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$ValidateTrustRequest;
    }
    export class Resource$Projects$Locations$Global$Domains$Backups {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a Backup for a domain.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes identified Backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Backup>>;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Get, options: MethodOptions | BodyResponseCallback<Schema$Backup>, callback: BodyResponseCallback<Schema$Backup>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Get, callback: BodyResponseCallback<Schema$Backup>): void;
        get(callback: BodyResponseCallback<Schema$Backup>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Backup in a given project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Global$Domains$Backups$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListBackupsResponse>>;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Backups$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Backups$List, options: MethodOptions | BodyResponseCallback<Schema$ListBackupsResponse>, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Backups$List, callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListBackupsResponse>): void;
        /**
         * Updates the labels for specified Backup.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Create extends StandardParameters {
        /**
         * Required. Backup Id, unique name to identify the backups with the following restrictions: * Must be lowercase letters, numbers, and hyphens * Must start with a letter. * Must contain between 1-63 characters. * Must end with a number or a letter. * Must be unique within the domain.
         */
        backupId?: string;
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Delete extends StandardParameters {
        /**
         * Required. The backup resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}/backups/{backup_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Get extends StandardParameters {
        /**
         * Required. The backup resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}/backups/{backup_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$List extends StandardParameters {
        /**
         * Optional. Filter specifying constraints of a list operation.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results following syntax at https://cloud.google.com/apis/design/design_patterns#sorting_order.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 1000 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The domain resource name using the form: `projects/{project_id\}/locations/global/domains/{domain_name\}`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Patch extends StandardParameters {
        /**
         * Output only. The unique name of the Backup in the form of projects/{project_id\}/locations/global/domains/{domain_name\}/backups/{name\}
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from Backup: * `labels`
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Backup;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Backups$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export class Resource$Projects$Locations$Global$Domains$Sqlintegrations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets details of a single sqlIntegration.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$SQLIntegration>>;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get, options: MethodOptions | BodyResponseCallback<Schema$SQLIntegration>, callback: BodyResponseCallback<Schema$SQLIntegration>): void;
        get(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get, callback: BodyResponseCallback<Schema$SQLIntegration>): void;
        get(callback: BodyResponseCallback<Schema$SQLIntegration>): void;
        /**
         * Lists SQLIntegrations in a given domain.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListSQLIntegrationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List, options: MethodOptions | BodyResponseCallback<Schema$ListSQLIntegrationsResponse>, callback: BodyResponseCallback<Schema$ListSQLIntegrationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List, callback: BodyResponseCallback<Schema$ListSQLIntegrationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListSQLIntegrationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$Get extends StandardParameters {
        /**
         * Required. MangedOU resource name using the form: `projects/{project_id\}/locations/global/domains/x/sqlIntegrations/{name\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Domains$Sqlintegrations$List extends StandardParameters {
        /**
         * Optional. Filter specifying constraints of a list operation. For example, `SqlIntegration.name="sql"`.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results following syntax at https://cloud.google.com/apis/design/design_patterns#sorting_order.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 1000 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response'ANIZATIONs next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the SqlIntegrations using the form: `projects/{project_id\}/locations/global/domains/x`
         */
        parent?: string;
    }
    export class Resource$Projects$Locations$Global$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        cancel(params: Params$Resource$Projects$Locations$Global$Operations$Cancel, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        cancel(params?: Params$Resource$Projects$Locations$Global$Operations$Cancel, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        cancel(params: Params$Resource$Projects$Locations$Global$Operations$Cancel, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        cancel(params: Params$Resource$Projects$Locations$Global$Operations$Cancel, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(params: Params$Resource$Projects$Locations$Global$Operations$Cancel, callback: BodyResponseCallback<Schema$Empty>): void;
        cancel(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Global$Operations$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Global$Operations$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Empty>>;
        delete(params: Params$Resource$Projects$Locations$Global$Operations$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Operations$Delete, options: MethodOptions | BodyResponseCallback<Schema$Empty>, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Operations$Delete, callback: BodyResponseCallback<Schema$Empty>): void;
        delete(callback: BodyResponseCallback<Schema$Empty>): void;
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Global$Operations$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Global$Operations$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        get(params: Params$Resource$Projects$Locations$Global$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Global$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Projects$Locations$Global$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Global$Operations$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Global$Operations$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListOperationsResponse>>;
        list(params: Params$Resource$Projects$Locations$Global$Operations$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Global$Operations$List, options: MethodOptions | BodyResponseCallback<Schema$ListOperationsResponse>, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Global$Operations$List, callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListOperationsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Operations$Cancel extends StandardParameters {
        /**
         * The name of the operation resource to be cancelled.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$CancelOperationRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Operations$Delete extends StandardParameters {
        /**
         * The name of the operation resource to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Operations$List extends StandardParameters {
        /**
         * The standard list filter.
         */
        filter?: string;
        /**
         * The name of the operation's parent resource.
         */
        name?: string;
        /**
         * The standard list page size.
         */
        pageSize?: number;
        /**
         * The standard list page token.
         */
        pageToken?: string;
    }
    export class Resource$Projects$Locations$Global$Peerings {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a Peering for Managed AD instance.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Global$Peerings$Create, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        create(params?: Params$Resource$Projects$Locations$Global$Peerings$Create, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        create(params: Params$Resource$Projects$Locations$Global$Peerings$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Global$Peerings$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Global$Peerings$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes identified Peering.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Global$Peerings$Delete, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        delete(params?: Params$Resource$Projects$Locations$Global$Peerings$Delete, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        delete(params: Params$Resource$Projects$Locations$Global$Peerings$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Peerings$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Global$Peerings$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets details of a single Peering.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Global$Peerings$Get, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        get(params?: Params$Resource$Projects$Locations$Global$Peerings$Get, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Peering>>;
        get(params: Params$Resource$Projects$Locations$Global$Peerings$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Global$Peerings$Get, options: MethodOptions | BodyResponseCallback<Schema$Peering>, callback: BodyResponseCallback<Schema$Peering>): void;
        get(params: Params$Resource$Projects$Locations$Global$Peerings$Get, callback: BodyResponseCallback<Schema$Peering>): void;
        get(callback: BodyResponseCallback<Schema$Peering>): void;
        /**
         * Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        getIamPolicy(params?: Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        getIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Lists Peerings in a given project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Global$Peerings$List, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        list(params?: Params$Resource$Projects$Locations$Global$Peerings$List, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$ListPeeringsResponse>>;
        list(params: Params$Resource$Projects$Locations$Global$Peerings$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Global$Peerings$List, options: MethodOptions | BodyResponseCallback<Schema$ListPeeringsResponse>, callback: BodyResponseCallback<Schema$ListPeeringsResponse>): void;
        list(params: Params$Resource$Projects$Locations$Global$Peerings$List, callback: BodyResponseCallback<Schema$ListPeeringsResponse>): void;
        list(callback: BodyResponseCallback<Schema$ListPeeringsResponse>): void;
        /**
         * Updates the labels for specified Peering.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Global$Peerings$Patch, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        patch(params?: Params$Resource$Projects$Locations$Global$Peerings$Patch, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Operation>>;
        patch(params: Params$Resource$Projects$Locations$Global$Peerings$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Peerings$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Global$Peerings$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        setIamPolicy(params?: Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$Policy>>;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy, options: MethodOptions | BodyResponseCallback<Schema$Policy>, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(params: Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy, callback: BodyResponseCallback<Schema$Policy>): void;
        setIamPolicy(callback: BodyResponseCallback<Schema$Policy>): void;
        /**
         * Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions, options: StreamMethodOptions): Promise<GaxiosResponseWithHTTP2<Readable>>;
        testIamPermissions(params?: Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions, options?: MethodOptions): Promise<GaxiosResponseWithHTTP2<Schema$TestIamPermissionsResponse>>;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions, options: MethodOptions | BodyResponseCallback<Schema$TestIamPermissionsResponse>, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(params: Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions, callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
        testIamPermissions(callback: BodyResponseCallback<Schema$TestIamPermissionsResponse>): void;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Create extends StandardParameters {
        /**
         * Required. Resource project name and location using the form: `projects/{project_id\}/locations/global`
         */
        parent?: string;
        /**
         * Required. Peering Id, unique name to identify peering.
         */
        peeringId?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Peering;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Delete extends StandardParameters {
        /**
         * Required. Peering resource name using the form: `projects/{project_id\}/locations/global/peerings/{peering_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Get extends StandardParameters {
        /**
         * Required. Peering resource name using the form: `projects/{project_id\}/locations/global/peerings/{peering_id\}`
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Getiampolicy extends StandardParameters {
        /**
         * Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
         */
        'options.requestedPolicyVersion'?: number;
        /**
         * REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$List extends StandardParameters {
        /**
         * Optional. Filter specifying constraints of a list operation. For example, `peering.authoized_network ="/projects/myprojectid"`.
         */
        filter?: string;
        /**
         * Optional. Specifies the ordering of results following syntax at https://cloud.google.com/apis/design/design_patterns#sorting_order.
         */
        orderBy?: string;
        /**
         * Optional. The maximum number of items to return. If not specified, a default value of 1000 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.
         */
        pageSize?: number;
        /**
         * Optional. The next_page_token value returned from a previous List request, if any.
         */
        pageToken?: string;
        /**
         * Required. The resource name of the domain location using the form: `projects/{project_id\}/locations/global`
         */
        parent?: string;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Patch extends StandardParameters {
        /**
         * Output only. Unique name of the peering in this scope including projects and location using the form: `projects/{project_id\}/locations/global/peerings/{peering_id\}`.
         */
        name?: string;
        /**
         * Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from Peering: * `labels`
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$Peering;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Setiampolicy extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$SetIamPolicyRequest;
    }
    export interface Params$Resource$Projects$Locations$Global$Peerings$Testiampermissions extends StandardParameters {
        /**
         * REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.
         */
        resource?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$TestIamPermissionsRequest;
    }
    export {};
}
