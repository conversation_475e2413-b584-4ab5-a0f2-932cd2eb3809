import { IActiveStatus, Identifiable } from '../Common/common.interface';
import { IRole } from '../Organization/role.interface';
import { IShift } from '../Organization/shift.interface';
import { IUserDetails } from './userDetails.interface';

export interface IUser {
  id?: number;
  avatar?: string;
  image?: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  address?: IAddress;
  phone?: string;
}
interface IAddress {
  street: string;
  suite: string;
  city: string;
  zipcode: string;
}

export interface IUserData extends Identifiable, IActiveStatus {
  sPassword: string;
  sEmail: string;
  aPhoneNumber: number;
  tBranches: any[];
  tRole: IRole;
  bCanLogin: boolean;
  bOnlyOfficePunch: boolean;
  tShift: IShift[];
  bIsApplicableAdvance: boolean;
  bIsResigned: boolean;
  bIsCreatedBySuperAdmin: boolean;
  bIsPermanentWFH: boolean;
  tIdEmployee: IEmployeeId;
  tUserDetails: IUserDetails;
  sProfileUrl?: string;
}

export interface IEmployeeId extends IEmployeeCodeInfoDTO {
  tRole: string;
}

export interface IEmployeeCodeInfoDTO extends Identifiable {
  sCode: string;
  aPunchId: number;
}
