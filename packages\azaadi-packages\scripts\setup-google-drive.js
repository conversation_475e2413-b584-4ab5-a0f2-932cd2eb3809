const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function setupGoogleDrive() {
  console.log('🔧 Google Drive Setup for Azaadi Packages\n');
  console.log('This script will help you configure Google Drive upload for your package builds.\n');
  
  console.log('📋 You will need:');
  console.log('1. Google Drive Folder ID (from the folder URL)');
  console.log('2. Google Cloud Console API credentials');
  console.log('3. OAuth2 refresh token\n');
  
  const proceed = await question('Do you want to continue? (y/n): ');
  if (proceed.toLowerCase() !== 'y') {
    console.log('Setup cancelled.');
    rl.close();
    return;
  }

  console.log('\n📁 Step 1: Google Drive Folder');
  console.log('Create a folder in Google Drive and copy its ID from the URL.');
  console.log('Example: https://drive.google.com/drive/folders/1ABC123DEF456GHI789JKL');
  console.log('The folder ID would be: 1ABC123DEF456GHI789JKL\n');
  
  const folderId = await question('Enter your Google Drive Folder ID: ');

  console.log('\n🔑 Step 2: Google Cloud Console Setup');
  console.log('1. Go to https://console.cloud.google.com/');
  console.log('2. Create a new project or select existing one');
  console.log('3. Enable Google Drive API');
  console.log('4. Create OAuth2 credentials (Desktop application)');
  console.log('5. Download the credentials JSON file\n');
  
  const clientId = await question('Enter your Client ID: ');
  const clientSecret = await question('Enter your Client Secret: ');

  console.log('\n🔄 Step 3: Refresh Token');
  console.log('You need to generate a refresh token using OAuth2 flow.');
  console.log('You can use Google OAuth2 Playground: https://developers.google.com/oauthplayground/');
  console.log('Or use a tool like Postman to get the refresh token.\n');
  
  const refreshToken = await question('Enter your Refresh Token: ');

  // Create .env file
  const envContent = `# Google Drive Configuration for Azaadi Packages
# Generated on ${new Date().toISOString()}

# Google Drive Folder ID where the .tgz files will be uploaded
GOOGLE_DRIVE_FOLDER_ID=${folderId}

# Google Drive API Credentials
GOOGLE_DRIVE_CLIENT_ID=${clientId}
GOOGLE_DRIVE_CLIENT_SECRET=${clientSecret}
GOOGLE_DRIVE_REFRESH_TOKEN=${refreshToken}

# Package configuration
PACKAGE_NAME=azaadi-packages
PACKAGE_VERSION=1.0.3
`;

  const envPath = path.join(process.cwd(), '.env');
  fs.writeFileSync(envPath, envContent);

  console.log('\n✅ Setup completed!');
  console.log(`📄 Configuration saved to: ${envPath}`);
  console.log('\n🚀 You can now use the following commands:');
  console.log('  npm run build-and-upload  - Build package and upload to Google Drive');
  console.log('  npm run pack-azaadi       - Build and create .tgz file locally');
  console.log('\n⚠️  Important: Add .env to your .gitignore to keep credentials secure!');
  
  rl.close();
}

// Add .env to .gitignore if not already present
function updateGitignore() {
  const gitignorePath = path.join(process.cwd(), '.gitignore');
  
  if (fs.existsSync(gitignorePath)) {
    const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
    
    if (!gitignoreContent.includes('.env')) {
      fs.appendFileSync(gitignorePath, '\n# Environment variables\n.env\n');
      console.log('✅ Added .env to .gitignore');
    }
  }
}

if (require.main === module) {
  setupGoogleDrive().then(() => {
    updateGitignore();
  }).catch(console.error);
}

module.exports = { setupGoogleDrive };
