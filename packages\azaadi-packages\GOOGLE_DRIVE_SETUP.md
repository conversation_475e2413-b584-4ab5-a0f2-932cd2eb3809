# Google Drive Auto-Upload Setup for Azaadi Packages

This guide will help you set up automatic uploading of your built package (.tgz files) to Google Drive.

## Quick Setup

Run the setup script to configure Google Drive upload:

```bash
npm run setup-google-drive
```

This interactive script will guide you through the configuration process.

## Manual Setup

### Step 1: Create Google Drive Folder

1. Go to [Google Drive](https://drive.google.com)
2. Create a new folder for your packages (e.g., "Azaadi Packages")
3. Copy the folder ID from the URL:
   - URL: `https://drive.google.com/drive/folders/1ABC123DEF456GHI789JKL`
   - Folder ID: `1ABC123DEF456GHI789JKL`

### Step 2: Set up Google Cloud Console

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Drive API:
   - Go to "APIs & Services" > "Library"
   - Search for "Google Drive API"
   - Click "Enable"
4. Create OAuth2 credentials:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Desktop application"
   - Download the credentials JSON file

### Step 3: Get Refresh Token

You need to generate a refresh token using the OAuth2 flow. You can use:

- [Google OAuth2 Playground](https://developers.google.com/oauthplayground/)
- Postman
- Or any OAuth2 client

### Step 4: Configure Environment Variables

Create a `.env` file in the project root:

```env
# Google Drive Configuration
GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here
GOOGLE_DRIVE_CLIENT_ID=your_client_id_here
GOOGLE_DRIVE_CLIENT_SECRET=your_client_secret_here
GOOGLE_DRIVE_REFRESH_TOKEN=your_refresh_token_here

# Package configuration (optional)
PACKAGE_NAME=azaadi-packages
PACKAGE_VERSION=1.0.3
```

## Usage

### Build and Upload to Google Drive

```bash
npm run build-and-upload
```

This command will:
1. Build the azaadi-packages library
2. Create a .tgz file
3. Upload it to your configured Google Drive folder

### Build Package Only

```bash
npm run pack-azaadi
```

This creates the .tgz file locally without uploading.

### Upload Existing Package

```bash
npm run upload-to-drive
```

This uploads an existing .tgz file to Google Drive.

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GOOGLE_DRIVE_FOLDER_ID` | Google Drive folder ID where files will be uploaded | Yes |
| `GOOGLE_DRIVE_CLIENT_ID` | OAuth2 Client ID from Google Cloud Console | Yes |
| `GOOGLE_DRIVE_CLIENT_SECRET` | OAuth2 Client Secret from Google Cloud Console | Yes |
| `GOOGLE_DRIVE_REFRESH_TOKEN` | OAuth2 Refresh Token | Yes |
| `PACKAGE_NAME` | Package name (optional, defaults to azaadi-packages) | No |
| `PACKAGE_VERSION` | Package version (optional, read from package.json) | No |

## Security Notes

- Never commit your `.env` file to version control
- The `.env` file is already added to `.gitignore`
- Use environment variables in production/CI environments
- Keep your Google Cloud credentials secure

## Troubleshooting

### Upload fails with authentication error
- Check that your credentials are correct
- Verify that the refresh token is still valid
- Ensure the Google Drive API is enabled in your project

### Folder not found error
- Verify the Google Drive folder ID is correct
- Check that the folder exists and is accessible
- Ensure your OAuth2 credentials have access to the folder

### Build fails
- Make sure all dependencies are installed: `npm install`
- Check that the Angular CLI is working: `ng version`
- Verify the library builds successfully: `ng build azaadi-packages`

## File Structure

```
packages/azaadi-packages/
├── scripts/
│   ├── build-and-upload.js     # Main build and upload script
│   └── setup-google-drive.js   # Interactive setup script
├── .env.example                # Environment variables template
├── .env                        # Your actual environment variables (not in git)
└── GOOGLE_DRIVE_SETUP.md      # This documentation
```
