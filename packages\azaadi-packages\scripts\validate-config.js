require('dotenv').config();

function validateGoogleDriveConfig() {
  console.log('🔍 Validating Google Drive Configuration...\n');
  
  const requiredVars = [
    'GOOGLE_DRIVE_FOLDER_ID',
    'GOOGLE_DRIVE_CLIENT_ID', 
    'GOOGLE_DRIVE_CLIENT_SECRET',
    'GOOGLE_DRIVE_REFRESH_TOKEN'
  ];
  
  const missing = [];
  const present = [];
  
  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      present.push(varName);
      console.log(`✅ ${varName}: Set`);
    } else {
      missing.push(varName);
      console.log(`❌ ${varName}: Missing`);
    }
  });
  
  console.log('\n📊 Summary:');
  console.log(`✅ Configured: ${present.length}/${requiredVars.length}`);
  console.log(`❌ Missing: ${missing.length}/${requiredVars.length}`);
  
  if (missing.length > 0) {
    console.log('\n⚠️  Google Drive upload will be skipped until all variables are set.');
    console.log('Run "npm run setup-google-drive" to configure missing variables.');
    return false;
  } else {
    console.log('\n🎉 All Google Drive configuration variables are set!');
    console.log('You can now use "npm run build-and-upload" to upload packages.');
    return true;
  }
}

if (require.main === module) {
  validateGoogleDriveConfig();
}

module.exports = { validateGoogleDriveConfig };
