import { AttendanceStatus, AttendanceType } from '../../enums';
import { IStartDateEndDate } from '../Common';
import { IShift } from '../Organization';

export interface ITodaysAttendance extends IStartDateEndDate {
  eAttendanceType: AttendanceType; // Attendance type enum/id (e.g., Present, Absent, etc.)
  eEndAttendanceType: AttendanceType; // End attendance type (if differs from start)
  tShift: string; // Shift identifier or name
}
export interface IAttendanceRequest {
  organizationId: string;
  shiftId: string;
  currentDate: string;
  departmentId?: string;
}

export interface ITodaysAttendanceResponse {
  dStartDate: Date[]; // List of start dates
  dEndDate: Date[]; // List of end dates
  eAttendanceType: AttendanceType; // Common attendance type
  eEndAttendanceType: AttendanceType; // Common end attendance type
  tShift: IShift; // Common shift across all dates
}

export interface IAttendanceReport {
  report: IDayAttendance[];
}

export interface IDayAttendance {
  status: AttendanceStatus;
  calendarDates: Date | null;
}
