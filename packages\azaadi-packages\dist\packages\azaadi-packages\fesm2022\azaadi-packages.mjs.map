{"version": 3, "file": "azaadi-packages.mjs", "sources": ["../../../../projects/azaadi-packages/src/Admin/lib/enums/attendance.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/enums/managements.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/enums/request.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/enums/support.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/enums/ticket.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/enums/users.enum.ts", "../../../../projects/azaadi-packages/src/Admin/lib/interface/DTO/userInfo.dto.interface.ts", "../../../../projects/azaadi-packages/src/Admin/lib/utils/constants.ts", "../../../../projects/azaadi-packages/src/azaadi-packages.ts"], "sourcesContent": ["export enum AttendanceStatus {\r\n  PRESENT = 'Present',\r\n  Leave = 'Leave',\r\n  PUBLIC_HOLIDAY = 'Public Holiday',\r\n  ORGANIZATION_WEEKEND = 'Organization Weekend',\r\n  ERROR = 'Error',\r\n  TODAYS_DATE = \"Today's Date\",\r\n  LHD = 'Less Than Half Day',\r\n  HD = 'Half Day',\r\n}\r\n\r\nexport enum AttendanceType {\r\n  ONLINE = 0,\r\n  OFFLINE = 1,\r\n}\r\n", "export enum UserAccess {\r\n  READ,\r\n  WRITE,\r\n  ALL,\r\n}\r\nexport enum FileType {\r\n  pdf,\r\n  jpg,\r\n  jpeg,\r\n  doc,\r\n  excel,\r\n  xml,\r\n}\r\n\r\nexport enum ProcessType {\r\n  EXAM,\r\n  INTERVIEW,\r\n  DOCUMENT,\r\n  BACKGROUND,\r\n}\r\n\r\nexport declare enum InterviewMode {\r\n  ONLINE = 0,\r\n  OFFLINE = 1,\r\n}\r\n\r\nexport enum LeaveType {\r\n  EXAM,\r\n  PERSONAL,\r\n  SICK,\r\n  UNPAID,\r\n  OTHERS,\r\n}\r\n\r\nexport enum CalendarEventType {\r\n  HOLIDAY,\r\n  WEEKOFF,\r\n  EVENT,\r\n}\r\n\r\nexport enum OrganizationSettingType {\r\n  SICK_LEAVE,\r\n  PERSONAL_LEAVE,\r\n  EXAM_LEAVE,\r\n  OTHER_LEAVE,\r\n  UNPAID_LEAVE,\r\n  WORKING_HOUR,\r\n  DESCRIPTION,\r\n  BRAND_NAME,\r\n  LOGO,\r\n  PHONE,\r\n  ADDRESS,\r\n  TOTAL_EMPLOYEE,\r\n  HOLIDAY,\r\n  WEEKOFF,\r\n  SIGN_IN,\r\n  SIGN_OUT,\r\n  SALARY_CIRCLE_START_DATE,\r\n  SALARY_CIRCLE_END_DATE,\r\n}\r\n\r\nexport enum OrganizationSettingLabels {\r\n  Logo = 'Logo',\r\n  TotalAllowEmployee = 'Total Allow Employee',\r\n  SalaryDay = 'Salary Day',\r\n  Phone = 'Phone No.',\r\n  OfficialEmail = 'Official Email',\r\n  Website = 'Website',\r\n  Address = 'Address',\r\n  Description = 'Description',\r\n  IncentiveDay = 'Incentive Day',\r\n  TDSApplicable = 'TDS Deduction Not Applicable',\r\n}\r\n\r\nexport enum RoleSettingLabels {\r\n  LateLogin = 'Late Login (min)',\r\n  EarlyLogout = 'Early Logout (min)',\r\n  PunchIn = 'Punch In Time',\r\n  PunchOut = 'Punch Out Time',\r\n  WeekOff = 'Week Off',\r\n  Holidays = 'Calendar',\r\n  isAdmin = 'Admin?',\r\n}\r\n\r\nexport enum SalaryStructureCategory {\r\n  NONPF = 0,\r\n  PF1 = 1,\r\n  PF2 = 2,\r\n}\r\nexport enum UpdatedSalaryStructureCategory {\r\n  NONPF = 0,\r\n  PF = 1,\r\n  // PFESI = 2,\r\n}\r\nexport enum GovtSub {\r\n  ABRY = 0,\r\n  NotApplicable = 1,\r\n}\r\n\r\nexport enum SkillCategory {\r\n  Unskilled = 0,\r\n  SemiSkilled = 1,\r\n  Skilled = 2,\r\n  HighlySkilled = 3,\r\n  NotFollowed = 4,\r\n}\r\nexport enum AnnouncementType {\r\n  Announcement = 0,\r\n  Birthday = 1,\r\n  WorkAnniversary = 2,\r\n  CalendarEvent = 3,\r\n}\r\n\r\nexport enum IncentiveType {\r\n  Monthly = 'Monthly',\r\n  Quarterly = 'Quarterly',\r\n  Yearly = 'Yearly',\r\n  HalfYearly = 'Half yearly',\r\n}\r\n\r\nexport enum DeductType {\r\n  Monthly = 'Monthly',\r\n  Quarterly = 'Quarterly',\r\n  HalfYearly = 'Half Yearly',\r\n  Yearly = 'Yearly',\r\n}\r\nexport enum AdvanceStatus {\r\n  Request = 'Request',\r\n  Approved = 'Approved',\r\n  Pending = 'Pending',\r\n  HrApproved = 'Approved By Hr',\r\n  Rejected = 'Rejected',\r\n  Canceled = 'Canceled',\r\n}\r\n", "export enum RequestStatus {\r\n  APPROVED = 0,\r\n  PENDING = 1,\r\n  REJECTED = 2,\r\n  UNPAID = 3,\r\n  CANCELLED = 4,\r\n}\r\n\r\nexport enum RequestType {\r\n  SICK_LEAVE = 0,\r\n  CASUAL_LEAVE = 1,\r\n  WORK_FROM_HOME = 2,\r\n}\r\nexport enum RoleRequestLabel {\r\n  SICK_LEAVE = 'Sick Leave',\r\n  CASUAL_LEAVE = 'Casual Leave',\r\n  ANNUAL_LEAVE = 'Annual Leave',\r\n  MATERNITY_LEAVE = 'Maternity Leave',\r\n  PATERNITY_LEAVE = 'Paternity Leave',\r\n  BEREAVEMENT_LEAVE = 'Bereavement Leave',\r\n  WORK_FROM_HOME = 'Work From Home',\r\n}\r\n", "export enum emsSupportPriority {\r\n    High,\r\n    Medium,\r\n    Low\r\n}\r\nexport enum emsSupportType {\r\n    Request,\r\n    Incident\r\n}\r\nexport enum emsSupportCategory {\r\n    Request\r\n}", "export enum TicketPriority {\r\n  LOW_PRIORITY = 'LOW PRIORITY',\r\n  MEDIUM_PRIORITY = 'MEDIUM PRIORITY',\r\n  HIGH_PRIORITY = 'HIGH PRIORITY',\r\n}\r\n\r\nexport enum TicketDepartment {\r\n  IT_DEPARTMENT = 'IT DEPARTMENT',\r\n  HR_DEPARTMENT = 'HR DEPARTMENT',\r\n  FINANCE_DEPARTMENT = 'FINANCE DEPARTMENT',\r\n  DEV_DEPARTMENT = 'DEV DEPARTMENT',\r\n}\r\nexport enum TicketStatus {\r\n  OPEN = 'OPEN',\r\n  PICKED = 'PICKED',\r\n  CLOSED = 'CLOSED',\r\n}\r\nexport enum TicketCategory {\r\n  MANAGER = 'MANAGER',\r\n  HR_MANAGEMENT = 'HR_MANAGEMENT',\r\n  IT_SUPPORT = 'IT_SUPPORT',\r\n}\r\nexport enum TicketUrgency {\r\n  LOW = 4,\r\n  MEDIUM = 3,\r\n  HIGH = 2,\r\n  URGENT = 1,\r\n}\r\nexport enum TicketImpactCategory {\r\n  INDIVIDUAL = 'INDIVIDUAL',\r\n  TEAM = 'TEAM',\r\n  DEPARTMENT = 'DEPARTMENT',\r\n}\r\nexport enum DocumentType {\r\n  PDF = 'PDF',\r\n  IMAGE = 'IMAGE',\r\n  WORD = 'WORD',\r\n  EXCEL = 'EXCEL',\r\n  OTHER = 'OTHER',\r\n}\r\n", "export enum EmsAttendanceType {\r\n    ONLINE,\r\n    OFFLINE,\r\n}\r\n\r\nexport enum EmsUserLeaveStatus {\r\n    APPROVED,\r\n    PENDING,\r\n    REJECTED,\r\n}\r\nexport enum EmsAttendanceStatus {\r\n    FULL_DAY,\r\n    HALF_DAY,\r\n    ERROR,\r\n    ABSENT,\r\n    WEEK_OFF,\r\n    LEAVE,\r\n    HOLIDAY,\r\n    LESS_THAN_HALF_DAY\r\n};\r\nexport enum EmsUserRequestStatus {\r\n    APPROVED,\r\n    PENDING,\r\n    REJECTED,\r\n    UNPAID,\r\n    CANCEL\r\n};", "import { IUserInfo } from '../User';\r\n\r\n// Reuse from IUserInfo, omitting the fields that differ in DTO\r\nexport interface IUserInfoDTO\r\n  extends Omit<\r\n    IUserInfo,\r\n    'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'\r\n  > {\r\n  /** Role of the user (as string ID) */\r\n  tRoleId?: string;\r\n\r\n  /** Organization ID (as string) */\r\n  tOrganizationId?: string;\r\n\r\n  /** Department ID (as string) */\r\n  tDepartmentId?: string;\r\n\r\n  /** Password of the user (optional in DTO) */\r\n  sPassword?: string;\r\n\r\n  /** Employee Code (renamed in DTO) */\r\n  empCode: string;\r\n}\r\n// export interface IUserListDTO extends IE<PERSON>loyeeCodeInfoDTO {\r\n//   sEmail: string;\r\n//   sCode: string;\r\n//   aPhoneNumber: number;\r\n//   sName: string;\r\n//   sProfileUrl?: string;\r\n//   tOrganization: IOrganization;\r\n//   dJoiningDate: Date;\r\n//   tRole: IRole;\r\n//   bIsActive: boolean;\r\n// }\r\n", "export const Ems_CONSTANT_DATA = {\r\n  ADMIN: {\r\n    QCM: {\r\n      QUIZ_SUCCESS_MSG: 'fdfd dfdf',\r\n    },\r\n  },\r\n  USER: {\r\n    QCM: {\r\n      QUIZ_SUCCESS_MSG: 'fdfd dfdf',\r\n    },\r\n  },\r\n};\r\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './public-api';\n"], "names": [], "mappings": "IAAY;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC,IAAA,gBAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C;AAC7C,IAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,cAA4B;AAC5B,IAAA,gBAAA,CAAA,KAAA,CAAA,GAAA,oBAA0B;AAC1B,IAAA,gBAAA,CAAA,IAAA,CAAA,GAAA,UAAe;AACjB,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,GAS3B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACb,CAAC,EAHW,cAAc,KAAd,cAAc,GAGzB,EAAA,CAAA,CAAA;;ICdW;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,UAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,UAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAJW,UAAU,KAAV,UAAU,GAIrB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAPW,QAAQ,KAAR,QAAQ,GAOnB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,WAAA,CAAA,WAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACZ,CAAC,EALW,WAAW,KAAX,WAAW,GAKtB,EAAA,CAAA,CAAA;IAOW;AAAZ,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACR,CAAC,EANW,SAAS,KAAT,SAAS,GAMpB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACP,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,GAI5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,uBAAuB,EAAA;AACjC,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc;AACd,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,uBAAA,CAAA,uBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;AACZ,IAAA,uBAAA,CAAA,uBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;AACZ,IAAA,uBAAA,CAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,uBAAA,CAAA,uBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gBAAc;AACd,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ;AACR,IAAA,uBAAA,CAAA,uBAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB;AACxB,IAAA,uBAAA,CAAA,uBAAA,CAAA,wBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,wBAAsB;AACxB,CAAC,EAnBW,uBAAuB,KAAvB,uBAAuB,GAmBlC,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,yBAAyB,EAAA;AACnC,IAAA,yBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,yBAAA,CAAA,oBAAA,CAAA,GAAA,sBAA2C;AAC3C,IAAA,yBAAA,CAAA,WAAA,CAAA,GAAA,YAAwB;AACxB,IAAA,yBAAA,CAAA,OAAA,CAAA,GAAA,WAAmB;AACnB,IAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,gBAAgC;AAChC,IAAA,yBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,yBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,yBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B;AAC3B,IAAA,yBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B;AAC9B,IAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,8BAA8C;AAChD,CAAC,EAXW,yBAAyB,KAAzB,yBAAyB,GAWpC,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,kBAA8B;AAC9B,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,oBAAkC;AAClC,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,eAAyB;AACzB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,gBAA2B;AAC3B,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,UAAoB;AACpB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,QAAkB;AACpB,CAAC,EARW,iBAAiB,KAAjB,iBAAiB,GAQ5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,uBAAuB,EAAA;AACjC,IAAA,uBAAA,CAAA,uBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT,IAAA,uBAAA,CAAA,uBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACT,CAAC,EAJW,uBAAuB,KAAvB,uBAAuB,GAIlC,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,8BAA8B,EAAA;AACxC,IAAA,8BAAA,CAAA,8BAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT,IAAA,8BAAA,CAAA,8BAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAM;;AAER,CAAC,EAJW,8BAA8B,KAA9B,8BAA8B,GAIzC,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACnB,CAAC,EAHW,OAAO,KAAP,OAAO,GAGlB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AACf,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX,IAAA,aAAA,CAAA,aAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACjB,IAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AACjB,CAAC,EANW,aAAa,KAAb,aAAa,GAMxB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB,IAAA,gBAAA,CAAA,gBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,gBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,iBAAmB;AACnB,IAAA,gBAAA,CAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACnB,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,GAK3B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,aAA0B;AAC5B,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,UAAA,CAAA,YAAA,CAAA,GAAA,aAA0B;AAC1B,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EALW,UAAU,KAAV,UAAU,GAKrB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,gBAA6B;AAC7B,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACvB,CAAC,EAPW,aAAa,KAAb,aAAa,GAOxB,EAAA,CAAA,CAAA;;ICrIW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACf,CAAC,EANW,aAAa,KAAb,aAAa,GAMxB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAkB;AACpB,CAAC,EAJW,WAAW,KAAX,WAAW,GAItB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AACzB,IAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC;AACvC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACnC,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,GAQ3B,EAAA,CAAA,CAAA;;ICrBW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,kBAAA,CAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,kBAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACP,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,GAI7B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACtB,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACZ,CAAC,EAHW,cAAc,KAAd,cAAc,GAGzB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACX,CAAC,EAFW,kBAAkB,KAAlB,kBAAkB,GAE7B,EAAA,CAAA,CAAA;;ICXW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AACjC,CAAC,EAJW,cAAc,KAAd,cAAc,GAIzB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC;AACzC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACnC,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,GAK3B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EAJW,YAAY,KAAZ,YAAY,GAIvB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,cAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AAC3B,CAAC,EAJW,cAAc,KAAd,cAAc,GAIzB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACZ,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,oBAAoB,EAAA;AAC9B,IAAA,oBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AACzB,IAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,oBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AAC3B,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,GAI/B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACX,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EANW,YAAY,KAAZ,YAAY,GAMvB,EAAA,CAAA,CAAA;;ICvCW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACX,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,kBAAA,CAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACZ,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,GAI7B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,mBAAmB,EAAA;AAC3B,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,mBAAA,CAAA,mBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,mBAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,mBAAA,CAAA,mBAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,oBAAkB;AACtB,CAAC,EATW,mBAAmB,KAAnB,mBAAmB,GAS9B,EAAA,CAAA,CAAA;AAAA;IACW;AAAZ,CAAA,UAAY,oBAAoB,EAAA;AAC5B,IAAA,oBAAA,CAAA,oBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,oBAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,oBAAA,CAAA,oBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,oBAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,oBAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACV,CAAC,EANW,oBAAoB,KAApB,oBAAoB,GAM/B,EAAA,CAAA,CAAA;AAAA;;ACHD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjCa,MAAA,iBAAiB,GAAG;AAC/B,IAAA,KAAK,EAAE;AACL,QAAA,GAAG,EAAE;AACH,YAAA,gBAAgB,EAAE,WAAW;AAC9B,SAAA;AACF,KAAA;AACD,IAAA,IAAI,EAAE;AACJ,QAAA,GAAG,EAAE;AACH,YAAA,gBAAgB,EAAE,WAAW;AAC9B,SAAA;AACF,KAAA;;;ACVH;;AAEG;;;;"}