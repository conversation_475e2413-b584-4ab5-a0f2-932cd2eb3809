{"hash": "820f0492d201535520e9f7d721263e589f581de61e6ec6d4f373cdc796322b44", "types": [{"exports": ["AdvanceStatus", "AnnouncementType", "AttendanceStatus", "AttendanceType", "AzadiAttendanceReport", "AzadiEmployeeReport", "CalendarEventType", "DeductType", "DocumentType", "EmsAttendanceStatus", "EmsAttendanceType", "EmsUserLeaveStatus", "EmsUserRequestStatus", "Ems_CONSTANT_DATA", "FileType", "GovtSub", "IAccess", "IAction", "IActiveStatus", "IAllTicketDTO", "IAnniversary", "IAsset", "IAttendanceReport", "IAttendanceRequest", "IBirthday", "IChatModel", "IColumn", "IConcept", "ICreateTicketTemplateDTO", "IDayAttendance", "IDepartment", "IDepartmentDTO", "IDepartmentModel", "IDesignationInfoDTO", "IDocument", "IDocumentDTO", "IDocumentModel", "IEmployeeCodeInfoDTO", "IEmployeeId", "IHoliday", "IHttpResponse", "ILanguage", "ILeaveBalance", "ILeaveBalanceRequest", "ILoginResponse", "IMessage", "INameEntity", "INotification", "IOrganization", "IOrganizationBase", "IOrganizationDTO", "IOrganizationModel", "IPost", "IQuickLink", "IRequest", "IRole", "IRoleDTO", "IRoleModel", "IShift", "IStartDateEndDate", "ITagEntity", "ITagType", "ITicket", "ITicketComment", "ITicketDTO", "ITicketImpactDTO", "ITicketImpactModel", "ITicketModel", "ITicketObserverDTO", "ITicketObserverModel", "ITicketReferenceDTO", "ITicketStatusCountDto", "ITicketTemplateDTO", "ITimeStamp", "ITimeStampIdentifiable", "ITodaysAttendance", "ITodaysAttendanceResponse", "IUser", "IUserData", "IUserDetails", "IUserInfo", "IUserInfoDTO", "IUserInfoModel", "Identifiable", "IdentityInfo", "IdentityInfoValue", "IncentiveType", "InfoCardInput", "InterviewMode", "LeaveType", "OrganizationSettingLabels", "OrganizationSettingType", "ProcessType", "RequestStatus", "RequestType", "RoleRequestLabel", "RoleSettingLabels", "SalaryStructureCategory", "SkillCategory", "TicketCategory", "TicketDepartment", "TicketImpactCategory", "TicketPriority", "TicketSortPriority", "TicketStatus", "TicketUrgency", "UpdatedSalaryStructureCategory", "UserAccess", "emsSupportCategory", "emsSupportPriority", "emsSupportType"], "facadeModuleId": "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-typings\\azaadi-packages.d.ts", "isDynamicEntry": false, "isEntry": true, "isImplicitEntry": false, "moduleIds": ["D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/attendance.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/managements.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/request.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/support.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/ticket.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/users.enum.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/common.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/access.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/assets.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/calendar-event.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/organization.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/department.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/designation.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/holiday.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/post.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/shift.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/attendance.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/request.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/userDetails.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/role.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/user.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/apiResponse.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/document.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/frontend.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Dashboard/dashboard.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/department.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/document.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/organization.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/role.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Ticket/ticket.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Ticket/message.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/userInfo.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/ticket.dto.interface.d.ts", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/utils/constants.d.ts", "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-typings\\azaadi-packages.d.ts"], "name": "azaadi-packages.d", "type": "chunk", "dynamicImports": [], "fileName": "index.d.ts", "implicitlyLoadedBefore": [], "importedBindings": {"mongoose": ["Types", "Document", "SchemaTimestampsConfig", "<PERSON><PERSON><PERSON>"]}, "imports": ["mongoose"], "modules": {"D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/attendance.enum.d.ts": {"code": "declare enum AttendanceStatus {\n    PRESENT = \"Present\",\n    Leave = \"Leave\",\n    PUBLIC_HOLIDAY = \"Public Holiday\",\n    ORGANIZATION_WEEKEND = \"Organization Weekend\",\n    ERROR = \"Error\",\n    TODAYS_DATE = \"Today's Date\",\n    LHD = \"Less Than Half Day\",\n    HD = \"Half Day\"\n}\ndeclare enum AttendanceType {\n    ONLINE = 0,\n    OFFLINE = 1\n}", "originalLength": 355, "removedExports": [], "renderedExports": ["AttendanceStatus", "AttendanceType"], "renderedLength": 340}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/managements.enum.d.ts": {"code": "declare enum UserAccess {\n    READ = 0,\n    WRITE = 1,\n    ALL = 2\n}\ndeclare enum FileType {\n    pdf = 0,\n    jpg = 1,\n    jpeg = 2,\n    doc = 3,\n    excel = 4,\n    xml = 5\n}\ndeclare enum ProcessType {\n    EXAM = 0,\n    INTERVIEW = 1,\n    DOCUMENT = 2,\n    BACKGROUND = 3\n}\ndeclare enum InterviewMode {\n    ONLINE = 0,\n    OFFLINE = 1\n}\ndeclare enum LeaveType {\n    EXAM = 0,\n    PERSONAL = 1,\n    SICK = 2,\n    UNPAID = 3,\n    OTHERS = 4\n}\ndeclare enum CalendarEventType {\n    HOLIDAY = 0,\n    WEEKOFF = 1,\n    EVENT = 2\n}\ndeclare enum OrganizationSettingType {\n    SICK_LEAVE = 0,\n    PERSONAL_LEAVE = 1,\n    EXAM_LEAVE = 2,\n    OTHER_LEAVE = 3,\n    UNPAID_LEAVE = 4,\n    WORKING_HOUR = 5,\n    DESCRIPTION = 6,\n    BRAND_NAME = 7,\n    LOGO = 8,\n    PHONE = 9,\n    ADDRESS = 10,\n    TOTAL_EMPLOYEE = 11,\n    <PERSON><PERSON><PERSON>AY = 12,\n    WEEKOFF = 13,\n    SIGN_IN = 14,\n    SIGN_OUT = 15,\n    SALARY_CIRCLE_START_DATE = 16,\n    SALARY_CIRCLE_END_DATE = 17\n}\ndeclare enum OrganizationSettingLabels {\n    Logo = \"Logo\",\n    TotalAllowEmployee = \"Total Allow Employee\",\n    SalaryDay = \"Salary Day\",\n    Phone = \"Phone No.\",\n    OfficialEmail = \"Official Email\",\n    Website = \"Website\",\n    Address = \"Address\",\n    Description = \"Description\",\n    IncentiveDay = \"Incentive Day\",\n    TDSApplicable = \"TDS Deduction Not Applicable\"\n}\ndeclare enum RoleSettingLabels {\n    LateLogin = \"Late Login (min)\",\n    EarlyLogout = \"Early Logout (min)\",\n    PunchIn = \"Punch In Time\",\n    PunchOut = \"Punch Out Time\",\n    WeekOff = \"Week Off\",\n    Holidays = \"Calendar\",\n    isAdmin = \"Admin?\"\n}\ndeclare enum SalaryStructureCategory {\n    NONPF = 0,\n    PF1 = 1,\n    PF2 = 2\n}\ndeclare enum UpdatedSalaryStructureCategory {\n    NONPF = 0,\n    PF = 1\n}\ndeclare enum GovtSub {\n    ABRY = 0,\n    NotApplicable = 1\n}\ndeclare enum SkillCategory {\n    Unskilled = 0,\n    SemiSkilled = 1,\n    Skilled = 2,\n    HighlySkilled = 3,\n    NotFollowed = 4\n}\ndeclare enum AnnouncementType {\n    Announcement = 0,\n    Birthday = 1,\n    WorkAnniversary = 2,\n    CalendarEvent = 3\n}\ndeclare enum IncentiveType {\n    Monthly = \"Monthly\",\n    Quarterly = \"Quarterly\",\n    Yearly = \"Yearly\",\n    HalfYearly = \"Half yearly\"\n}\ndeclare enum DeductType {\n    Monthly = \"Monthly\",\n    Quarterly = \"Quarterly\",\n    HalfYearly = \"Half Yearly\",\n    Yearly = \"Yearly\"\n}\ndeclare enum AdvanceStatus {\n    Request = \"Request\",\n    Approved = \"Approved\",\n    Pending = \"Pending\",\n    HrApproved = \"Approved By Hr\",\n    Rejected = \"Rejected\",\n    Canceled = \"Canceled\"\n}", "originalLength": 2630, "removedExports": [], "renderedExports": ["UserAccess", "FileType", "ProcessType", "InterviewMode", "LeaveType", "CalendarEventType", "OrganizationSettingType", "OrganizationSettingLabels", "RoleSettingLabels", "SalaryStructureCategory", "UpdatedSalaryStructureCategory", "GovtSub", "SkillCategory", "AnnouncementType", "IncentiveType", "DeductType", "AdvanceStatus"], "renderedLength": 2510}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/request.enum.d.ts": {"code": "declare enum RequestStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2,\n    UNPAID = 3,\n    CANCELLED = 4\n}\ndeclare enum RequestType {\n    SICK_LEAVE = 0,\n    CASUAL_LEAVE = 1,\n    WORK_FROM_HOME = 2\n}\ndeclare enum RoleRequestLabel {\n    SICK_LEAVE = \"Sick Leave\",\n    CASUAL_LEAVE = \"Casual Leave\",\n    ANNUAL_LEAVE = \"Annual Leave\",\n    MATERNITY_LEAVE = \"Maternity Leave\",\n    PATERNITY_LEAVE = \"Paternity Leave\",\n    BEREAVEMENT_LEAVE = \"Bereavement Leave\",\n    WORK_FROM_HOME = \"Work From Home\"\n}", "originalLength": 533, "removedExports": [], "renderedExports": ["RequestStatus", "RequestType", "RoleRequestLabel"], "renderedLength": 511}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/support.enum.d.ts": {"code": "declare enum emsSupportPriority {\n    High = 0,\n    Medium = 1,\n    Low = 2\n}\ndeclare enum emsSupportType {\n    Request = 0,\n    Incident = 1\n}\ndeclare enum emsSupportCategory {\n    Request = 0\n}", "originalLength": 217, "removedExports": [], "renderedExports": ["emsSupportPriority", "emsSupportType", "emsSupportCategory"], "renderedLength": 195}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/ticket.enum.d.ts": {"code": "declare enum TicketPriority {\n    LOW_PRIORITY = \"LOW PRIORITY\",\n    MEDIUM_PRIORITY = \"MEDIUM PRIORITY\",\n    HIGH_PRIORITY = \"HIGH PRIORITY\"\n}\ndeclare enum TicketDepartment {\n    IT_DEPARTMENT = \"IT DEPARTMENT\",\n    HR_DEPARTMENT = \"HR DEPARTMENT\",\n    FINANCE_DEPARTMENT = \"FINANCE DEPARTMENT\",\n    DEV_DEPARTMENT = \"DEV DEPARTMENT\"\n}\ndeclare enum TicketStatus {\n    OPEN = \"OPEN\",\n    PICKED = \"PICKED\",\n    CLOSED = \"CLOSED\"\n}\ndeclare enum TicketCategory {\n    MANAGER = \"MANAGER\",\n    HR_MANAGEMENT = \"HR_MANAGEMENT\",\n    IT_SUPPORT = \"IT_SUPPORT\"\n}\ndeclare enum TicketUrgency {\n    LOW = 4,\n    MEDIUM = 3,\n    HIGH = 2,\n    URGENT = 1\n}\ndeclare enum TicketImpactCategory {\n    INDIVIDUAL = \"INDIVIDUAL\",\n    TEAM = \"TEAM\",\n    DEPARTMENT = \"DEPARTMENT\"\n}\ndeclare enum DocumentType {\n    PDF = \"PDF\",\n    IMAGE = \"IMAGE\",\n    WORD = \"WORD\",\n    EXCEL = \"EXCEL\",\n    OTHER = \"OTHER\"\n}", "originalLength": 939, "removedExports": [], "renderedExports": ["TicketPriority", "TicketDepartment", "TicketStatus", "TicketCategory", "TicketUrgency", "TicketImpactCategory", "DocumentType"], "renderedLength": 889}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/enums/users.enum.d.ts": {"code": "declare enum EmsAttendanceType {\n    ONLINE = 0,\n    OFFLINE = 1\n}\ndeclare enum EmsUserLeaveStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2\n}\ndeclare enum EmsAttendanceStatus {\n    FULL_DAY = 0,\n    HALF_DAY = 1,\n    ERROR = 2,\n    ABSENT = 3,\n    WEEK_OFF = 4,\n    LEAVE = 5,\n    HOLIDAY = 6,\n    LESS_THAN_HALF_DAY = 7\n}\ndeclare enum EmsUserRequestStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2,\n    UNPAID = 3,\n    CANCEL = 4\n}", "originalLength": 486, "removedExports": [], "renderedExports": ["EmsAttendanceType", "EmsUserLeaveStatus", "EmsAttendanceStatus", "EmsUserRequestStatus"], "renderedLength": 457}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/common.interface.d.ts": {"code": "interface Identifiable {\n    _id: string | Types.ObjectId;\n}\ninterface ITimeStamp {\n    createdAt?: Date;\n    updatedAt?: Date;\n}\ninterface INameEntity extends Identifiable {\n    sName: string;\n}\ninterface ITagEntity extends INameEntity {\n    sTag?: string;\n}\ninterface ITimeStampIdentifiable extends ITimeStamp, Identifiable {\n}\ninterface IActiveStatus {\n    bIsActive: boolean;\n}\ninterface IStartDateEndDate {\n    dStartDate: Date;\n    dEndDate: Date;\n}\ninterface IConcept extends Document, SchemaTimestampsConfig {\n}", "originalLength": 1924, "removedExports": [], "renderedExports": ["Identifiable", "ITimeStamp", "INameEntity", "ITagEntity", "ITimeStampIdentifiable", "IActiveStatus", "IStartDateEndDate", "IConcept"], "renderedLength": 519}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/access.interface.d.ts": {"code": "interface IAccess extends ITagEntity {\n    tRole: string;\n    bCanView: boolean;\n    bCanWrite: boolean;\n}", "originalLength": 163, "removedExports": [], "renderedExports": ["IAccess"], "renderedLength": 106}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/assets.interface.d.ts": {"code": "interface IAsset {\n    id: number;\n    name: string;\n    imageUrl: string;\n    dateAcquired: Date;\n    status: string;\n}", "originalLength": 128, "removedExports": [], "renderedExports": ["IAsset"], "renderedLength": 120}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/calendar-event.interface.d.ts": {"code": "interface IAnniversary {\n    user: IUser;\n    anniversaryDate: Date;\n}\ninterface IBirthday {\n    user: IUser;\n    birthdayDate: Date;\n}", "originalLength": 183, "removedExports": [], "renderedExports": ["IAnniversary", "IBirthday"], "renderedLength": 135}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/organization.interface.d.ts": {"code": "interface IOrganizationBase {\n    /** Tag/identifier for the organization */\n    sTag: string;\n    /** Name of the organization */\n    sName: string;\n    /** Description of the organization */\n    sDescription: string;\n    /** Branch /company name */\n    sBrandName: string;\n    /** Logo URL or path */\n    sLogo: string;\n    /** Organization's email address */\n    sEmail: string;\n}\ninterface IOrganization extends IOrganizationBase, Identifiable {\n}\ninterface IOrganizationModel extends IConcept, IOrganizationBase {\n}", "originalLength": 611, "removedExports": [], "renderedExports": ["IOrganizationBase", "IOrganization", "IOrganizationModel"], "renderedLength": 520}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/department.interface.d.ts": {"code": "interface IDepartment {\n    /** Name of the department */\n    sName: string;\n    /** Reference to the department head */\n    tDepartmentHead: string | Types.ObjectId | IUserInfo;\n    /** Reference to the parent organization */\n    tOrganization: string | Types.ObjectId | IOrganization;\n}\ninterface IDepartmentModel extends IConcept, IDepartment {\n}", "originalLength": 1828, "removedExports": [], "renderedExports": ["IDepartment", "IDepartmentModel"], "renderedLength": 349}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/designation.interface.d.ts": {"code": "interface IDesignationInfoDTO extends ITagEntity {\n}", "originalLength": 117, "removedExports": [], "renderedExports": ["IDesignationInfoDTO"], "renderedLength": 52}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/holiday.interface.d.ts": {"code": "interface IHoliday {\n    title: string;\n    date: Date;\n    description: string;\n    image: string;\n}", "originalLength": 109, "removedExports": [], "renderedExports": ["IHoliday"], "renderedLength": 101}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/post.interface.d.ts": {"code": "interface IPost {\n    title?: string;\n    body: string;\n    user: IUser;\n    date: Date;\n}", "originalLength": 146, "removedExports": [], "renderedExports": ["IPost"], "renderedLength": 90}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/shift.interface.d.ts": {"code": "interface IShift extends INameEntity {\n    sTimezone: string;\n    sTime: string;\n    sPunchInTime: string;\n    sPunchOutTime: string;\n    sTimeBuffer: string;\n    isDefault: boolean;\n    tOrganization: IOrganization;\n}", "originalLength": 342, "removedExports": [], "renderedExports": ["IShift"], "renderedLength": 218}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/attendance.interface.d.ts": {"code": "interface ITodaysAttendance extends IStartDateEndDate {\n    eAttendanceType: AttendanceType;\n    eEndAttendanceType: AttendanceType;\n    tShift: string;\n}\ninterface IAttendanceRequest {\n    organizationId: string;\n    shiftId: string;\n    currentDate: string;\n    departmentId?: string;\n}\ninterface ITodaysAttendanceResponse {\n    dStartDate: Date[];\n    dEndDate: Date[];\n    eAttendanceType: AttendanceType;\n    eEndAttendanceType: AttendanceType;\n    tShift: IShift;\n}\ninterface IAttendanceReport {\n    report: IDayAttendance[];\n}\ninterface IDayAttendance {\n    status: AttendanceStatus;\n    calendarDates: Date | null;\n}", "originalLength": 813, "removedExports": [], "renderedExports": ["ITodaysAttendance", "IAttendanceRequest", "ITodaysAttendanceResponse", "IAttendanceReport", "IDayAttendance"], "renderedLength": 624}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/request.interface.d.ts": {"code": "interface IRequest {\n    dDate: Date[];\n    eStatus: number;\n    eType: string;\n    tSender: string;\n    sReason: string;\n    sMessage: string;\n    tApproverBy: string;\n}\ninterface ILeaveBalance extends Identifiable {\n    sType: RoleRequestLabel;\n    aTotalBalance: 5;\n    aAvailableBalance: 5;\n}\ninterface ILeaveBalanceRequest {\n    organizationId: string;\n    currentYear: number;\n}", "originalLength": 496, "removedExports": [], "renderedExports": ["IRequest", "ILeaveBalance", "ILeaveBalanceRequest"], "renderedLength": 384}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/userDetails.interface.d.ts": {"code": "interface IUserDetails extends Identifiable, IActiveStatus {\n    sName: string;\n    tDepartment: IDepartment;\n    dJoinDate: string;\n    tOrganization: IOrganization;\n    tEducationInfo: any[];\n    tExperienceInfo: any[];\n}\ninterface IUserInfo {\n    /** Full name of the user */\n    sName: string;\n    /** Email address of the user */\n    sEmail: string;\n    /** Hashed password of the user */\n    sPassword: string;\n    /** ProfilePicture of the user */\n    sProfilePicture: string;\n    /** Role of the user */\n    tRoleId: string | Types.ObjectId | IRole;\n    /** EmployeeCode of the user */\n    tEmpCode: string;\n    /** Status of the user */\n    eStatus: string;\n    /** Reference to the organization the user belongs to */\n    tOrganizationId: string | Types.ObjectId;\n    /** Reference to the user's role in the system */\n    tDepartmentId: string | Types.ObjectId;\n    /** Reference to the user's department */\n    sDeviceToken?: string;\n}\ninterface IUserInfoModel extends IConcept, IUserInfo {\n}", "originalLength": 2604, "removedExports": [], "renderedExports": ["IUserDetails", "IUserInfo", "IUserInfoModel"], "renderedLength": 1003}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Organization/role.interface.d.ts": {"code": "interface IRole {\n    /** Name of the role */\n    sName: string;\n    /** Tag/identifier for the role */\n    sTag: string;\n    /** Reference to the head/parent role */\n    tHead: string | Schema.Types.ObjectId | IUserInfo;\n    /** Reference to the organization this role belongs to */\n    tOrganization: string | Schema.Types.ObjectId | IOrganization;\n}\ninterface IRoleModel extends IConcept, IRole {\n}", "originalLength": 1881, "removedExports": [], "renderedExports": ["IRole", "IRoleModel"], "renderedLength": 401}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/User/user.interface.d.ts": {"code": "interface IUser {\n    id?: number;\n    avatar?: string;\n    image?: string;\n    name?: string;\n    firstName?: string;\n    lastName?: string;\n    username?: string;\n    email?: string;\n    address?: IAddress;\n    phone?: string;\n}\ninterface IAddress {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n}\ninterface IUserData extends Identifiable, IActiveStatus {\n    sPassword: string;\n    sEmail: string;\n    aPhoneNumber: number;\n    tBranches: any[];\n    tRole: IRole;\n    bCanLogin: boolean;\n    bOnlyOfficePunch: boolean;\n    tShift: IShift[];\n    bIsApplicableAdvance: boolean;\n    bIsResigned: boolean;\n    bIsCreatedBySuperAdmin: boolean;\n    bIsPermanentWFH: boolean;\n    tIdEmployee: IEmployeeId;\n    tUserDetails: IUserDetails;\n    sProfileUrl?: string;\n}\ninterface IEmployeeId extends IEmployeeCodeInfoDTO {\n    tRole: string;\n}\ninterface IEmployeeCodeInfoDTO extends Identifiable {\n    sCode: string;\n    aPunchId: number;\n}", "originalLength": 1248, "removedExports": [], "renderedExports": ["IUser", "IUserData", "IEmployeeId", "IEmployeeCodeInfoDTO"], "renderedLength": 964}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/apiResponse.interface.d.ts": {"code": "interface ILoginResponse {\n    success: boolean;\n    message: string;\n    data: IUserData;\n    headers: {\n        [key: string]: string;\n    };\n}\ninterface IHttpResponse<T> {\n    status: string;\n    statusCode: number;\n    data: T;\n}", "originalLength": 300, "removedExports": [], "renderedExports": ["ILoginResponse", "IHttpResponse"], "renderedLength": 233}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/document.interface.d.ts": {"code": "interface IDocument {\n    /** Name of the document */\n    sName: string;\n    /** URL or path to the document */\n    sUrl: string;\n    /** Type of the document */\n    eType: DocumentType;\n    /** Size of the document in bytes */\n    aSize: number;\n    /** MIME type of the document */\n    sMimeType: string;\n    /** Upload date of the document */\n    dUploadDate: Date;\n    /** Reference to the uploader */\n    tUploader: string | Types.ObjectId | IUserInfo;\n}\ninterface IDocumentModel extends IConcept, IDocument {\n}", "originalLength": 1973, "removedExports": [], "renderedExports": ["IDocument", "IDocumentModel"], "renderedLength": 516}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Common/frontend.interface.d.ts": {"code": "interface IAction {\n    icon?: string;\n    label: string;\n    action: () => void;\n}\ninterface ILanguage {\n    name: string;\n    code: string;\n}\ninterface IColumn {\n    field: string;\n    header: string;\n}\ninterface IdentityInfo extends ITagType {\n    title: string;\n    icon?: string;\n    tag?: string;\n    noOfCols: number;\n    values: IdentityInfoValue[];\n}\ninterface IdentityInfoValue {\n    label: string;\n    value: string | number;\n    valueHexColor?: string;\n}\ninterface InfoCardInput {\n    title: string;\n    values: InfoCardValue[];\n}\ninterface InfoCardValue {\n    label: string;\n    value: string | number;\n    valueHexColor?: string;\n}\ninterface ITagType {\n    tagType?: 'success' | 'info' | 'warning' | 'danger' | 'primary' | 'secondary' | 'light' | 'dark';\n}\ninterface IQuickLink {\n    label: string;\n    link: string;\n}", "originalLength": 900, "removedExports": [], "renderedExports": ["IAction", "ILanguage", "IColumn", "IdentityInfo", "IdentityInfoValue", "InfoCardInput", "ITagType", "IQuickLink"], "renderedLength": 832}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Dashboard/dashboard.interface.d.ts": {"code": "interface AzadiAttendanceReport {\n    aMonth: number;\n    aTotalDays: number;\n    aWeekends: number;\n    aBusinessDays: number;\n    aHolidays: number;\n    aWorkingDays: number;\n    aLeaves: number;\n    aPresentDay: number;\n}\ninterface AzadiEmployeeReport {\n    aMonth: number;\n    aNewJoin: number;\n    aTotalLeft: number;\n}", "originalLength": 339, "removedExports": [], "renderedExports": ["AzadiAttendanceReport", "AzadiEmployeeReport"], "renderedLength": 324}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/department.dto.interface.d.ts": {"code": "interface IDepartmentDTO extends IDepartment {\n}", "originalLength": 103, "removedExports": [], "renderedExports": ["IDepartmentDTO"], "renderedLength": 48}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/document.dto.interface.d.ts": {"code": "interface IDocumentDTO extends IDocument {\n}", "originalLength": 110, "removedExports": [], "renderedExports": ["IDocumentDTO"], "renderedLength": 44}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/organization.dto.interface.d.ts": {"code": "interface IOrganizationDTO extends IOrganization {\n}", "originalLength": 109, "removedExports": [], "renderedExports": ["IOrganizationDTO"], "renderedLength": 52}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/role.dto.interface.d.ts": {"code": "interface IRoleDTO extends IRole {\n}", "originalLength": 85, "removedExports": [], "renderedExports": ["IRoleDTO"], "renderedLength": 36}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Ticket/ticket.interface.d.ts": {"code": "interface ITicketComment {\n    id: number;\n    comment: string;\n    createdAt: Date;\n    createdByUserId: number;\n    ticket: ITicket;\n}\ninterface ITicket extends ITicketBase, Identifiable {\n}\ninterface ITicketBase {\n    /** Reference to the organization */\n    tOrganization: string | Schema.Types.ObjectId | IOrganization;\n    /** User who requested the ticket */\n    tRequester: string | Schema.Types.ObjectId | IUserInfo;\n    /** Current status of the ticket */\n    eStatusKey: TicketStatus;\n    /** Category of the ticket */\n    eCategoryKey: TicketCategory;\n    /** Urgency level of the ticket */\n    eUrgency: TicketUrgency;\n    /** Impact category of the ticket */\n    eImpactCategory: TicketImpactCategory;\n    /** User impacted by the ticket */\n    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n    /** User who approved the ticket */\n    tApprover: string | Schema.Types.ObjectId | IUserInfo;\n    /** User assigned to handle the ticket */\n    tAssignedTo: string | Schema.Types.ObjectId | IUserInfo;\n    /** Observer of the ticket */\n    tObserver: string | Schema.Types.ObjectId | IUserInfo;\n    /** Associated asset with the ticket */\n    tAsset: Record<string, any>;\n    /** Description of the ticket */\n    sDescription: string;\n    /** Title of the ticket */\n    sTitle: string;\n    /** Incident date */\n    dIncidentDate: Date;\n    /** Array of attached documents */\n    aDocuments: IDocument[];\n}\ninterface ITicketModel extends IConcept, ITicketBase {\n}\ninterface ITicketIdentifiable extends IConcept {\n    tTicketId: string | Schema.Types.ObjectId | ITicket;\n}\ninterface ITicketImpactModel extends ITicketIdentifiable {\n    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n}\ninterface ITicketObserverModel extends ITicketIdentifiable {\n    tObserver: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n}", "originalLength": 3507, "removedExports": [], "renderedExports": ["ITicketComment", "ITicket", "ITicketModel", "ITicketImpactModel", "ITicketObserverModel"], "renderedLength": 1852}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/Ticket/message.interface.d.ts": {"code": "interface IMessage {\n    sender: IUser;\n    content: string;\n    date: Date;\n    isRead?: boolean;\n    receiver?: IUser;\n}\ninterface INotification {\n    subject: string;\n    message: string;\n}\ninterface IChatModel extends IConcept {\n    tTicketId: string | ITicketModel;\n    tSenderId: string | IUserInfoModel;\n    sContent: string;\n}", "originalLength": 494, "removedExports": [], "renderedExports": ["IMessage", "INotification", "IChatModel"], "renderedLength": 334}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/userInfo.dto.interface.d.ts": {"code": "interface IUserInfoDTO extends Omit<IUserInfo, 'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'> {\n    /** Role of the user (as string ID) */\n    tRoleId?: string;\n    /** Organization ID (as string) */\n    tOrganizationId?: string;\n    /** Department ID (as string) */\n    tDepartmentId?: string;\n    /** Password of the user (optional in DTO) */\n    sPassword?: string;\n    /** Employee Code (renamed in DTO) */\n    empCode: string;\n}", "originalLength": 507, "removedExports": [], "renderedExports": ["IUserInfoDTO"], "renderedLength": 462}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/interface/DTO/ticket.dto.interface.d.ts": {"code": "/**\n * DTO Interface for Ticket\n */\ninterface ITicketDTO extends Omit<ITicket, 'tRequester' | 'tImpactUser' | 'tApprover' | 'tAssignedTo' | 'tObserver' | 'aDocuments'> {\n    /** User who requested the ticket */\n    tRequester?: string | IUserInfoDTO;\n    /** User impacted by the ticket */\n    tImpactUsers?: string[] | IUserInfoDTO[];\n    /** User who approved the ticket */\n    tApprover?: string | IUserInfoDTO;\n    /** User assigned to handle the ticket */\n    tAssignedTo?: string | IUserInfoDTO;\n    /** Observer(s) of the ticket */\n    tObserver?: string[] | IUserInfoDTO[];\n    /** Array of attached documents */\n    aDocuments?: string[] | IDocumentDTO[];\n}\ninterface IAllTicketDTO {\n    tickets: ITicketDTO[];\n    count: number;\n}\ninterface ITicketReferenceDTO {\n    /** Reference to the ticket */\n    tTicketId: string | ITicketDTO;\n}\ninterface ITicketObserverDTO extends ITicketReferenceDTO {\n    /** Observers of the ticket */\n    tObserver: string[] | IUserInfoDTO[];\n}\ninterface ITicketImpactDTO extends ITicketReferenceDTO {\n    /** Users impacted by the ticket */\n    tImpactUser: string[] | IUserInfo[];\n}\ninterface ICreateTicketTemplateDTO {\n    tOrganization: string;\n    tRequester: string;\n    eStatusKey: string;\n    eCategoryKey: string;\n    eUrgency: string;\n    eImpactCategory: TicketImpactCategory;\n    tImpactUser: string;\n    tApprover: string;\n    tAssignedTo: string;\n    tObserver: string[];\n    tAsset: string;\n    sDescription: string;\n    sTitle: string;\n    dIncidentDate: Date;\n    aDocuments: string[];\n}\ninterface ITicketTemplateDTO {\n    department: string;\n    searchText?: string;\n    endDate?: string;\n    startDate?: string;\n    priority?: TicketSortPriority;\n}\ntype TicketSortPriority = {\n    _id?: 1 | -1;\n    sTitle?: 1 | -1;\n    eUrgency?: 1 | -1;\n    dIncidentDate?: 1 | -1;\n    'tRequester.sName'?: 1 | -1;\n    'tAssignedTo.sName'?: 1 | -1;\n};\ninterface ITicketStatusCountDto {\n    total: number;\n    open: number;\n    closed: number;\n}", "originalLength": 2291, "removedExports": [], "renderedExports": ["ITicketDTO", "IAllTicketDTO", "ITicketReferenceDTO", "ITicketObserverDTO", "ITicketImpactDTO", "ICreateTicketTemplateDTO", "ITicketTemplateDTO", "TicketSortPriority", "ITicketStatusCountDto"], "renderedLength": 1987}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-typings/Admin/lib/utils/constants.d.ts": {"code": "declare const Ems_CONSTANT_DATA: {\n    ADMIN: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: string;\n        };\n    };\n    USER: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: string;\n        };\n    };\n};", "originalLength": 212, "removedExports": [], "renderedExports": ["Ems_CONSTANT_DATA"], "renderedLength": 204}, "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-typings\\azaadi-packages.d.ts": {"code": null, "originalLength": 120, "removedExports": [], "renderedExports": [], "renderedLength": 0}}, "referencedFiles": [], "code": "/// <reference types=\"mongoose/types/aggregate\" />\n/// <reference types=\"mongoose/types/callback\" />\n/// <reference types=\"mongoose/types/collection\" />\n/// <reference types=\"mongoose/types/connection\" />\n/// <reference types=\"mongoose/types/cursor\" />\n/// <reference types=\"mongoose/types/document\" />\n/// <reference types=\"mongoose/types/error\" />\n/// <reference types=\"mongoose/types/expressions\" />\n/// <reference types=\"mongoose/types/helpers\" />\n/// <reference types=\"mongoose/types/middlewares\" />\n/// <reference types=\"mongoose/types/indexes\" />\n/// <reference types=\"mongoose/types/models\" />\n/// <reference types=\"mongoose/types/mongooseoptions\" />\n/// <reference types=\"mongoose/types/pipelinestage\" />\n/// <reference types=\"mongoose/types/populate\" />\n/// <reference types=\"mongoose/types/query\" />\n/// <reference types=\"mongoose/types/schemaoptions\" />\n/// <reference types=\"mongoose/types/session\" />\n/// <reference types=\"mongoose/types/types\" />\n/// <reference types=\"mongoose/types/utility\" />\n/// <reference types=\"mongoose/types/validation\" />\n/// <reference types=\"mongoose/types/virtuals\" />\n/// <reference types=\"mongoose/types/schematypes\" />\n/// <reference types=\"mongoose/types/inferschematype\" />\n/// <reference types=\"mongoose/types/inferrawdoctype\" />\nimport { Types, Document, SchemaTimestampsConfig, Schema } from 'mongoose';\n\ndeclare enum AttendanceStatus {\n    PRESENT = \"Present\",\n    Leave = \"Leave\",\n    PUBLIC_HOLIDAY = \"Public Holiday\",\n    ORGANIZATION_WEEKEND = \"Organization Weekend\",\n    ERROR = \"Error\",\n    TODAYS_DATE = \"Today's Date\",\n    LHD = \"Less Than Half Day\",\n    HD = \"Half Day\"\n}\ndeclare enum AttendanceType {\n    ONLINE = 0,\n    OFFLINE = 1\n}\n\ndeclare enum UserAccess {\n    READ = 0,\n    WRITE = 1,\n    ALL = 2\n}\ndeclare enum FileType {\n    pdf = 0,\n    jpg = 1,\n    jpeg = 2,\n    doc = 3,\n    excel = 4,\n    xml = 5\n}\ndeclare enum ProcessType {\n    EXAM = 0,\n    INTERVIEW = 1,\n    DOCUMENT = 2,\n    BACKGROUND = 3\n}\ndeclare enum InterviewMode {\n    ONLINE = 0,\n    OFFLINE = 1\n}\ndeclare enum LeaveType {\n    EXAM = 0,\n    PERSONAL = 1,\n    SICK = 2,\n    UNPAID = 3,\n    OTHERS = 4\n}\ndeclare enum CalendarEventType {\n    HOLIDAY = 0,\n    WEEKOFF = 1,\n    EVENT = 2\n}\ndeclare enum OrganizationSettingType {\n    SICK_LEAVE = 0,\n    PERSONAL_LEAVE = 1,\n    EXAM_LEAVE = 2,\n    OTHER_LEAVE = 3,\n    UNPAID_LEAVE = 4,\n    WORKING_HOUR = 5,\n    DESCRIPTION = 6,\n    BRAND_NAME = 7,\n    LOGO = 8,\n    PHONE = 9,\n    ADDRESS = 10,\n    TOTAL_EMPLOYEE = 11,\n    HOLIDAY = 12,\n    WEEKOFF = 13,\n    SIGN_IN = 14,\n    SIGN_OUT = 15,\n    SALARY_CIRCLE_START_DATE = 16,\n    SALARY_CIRCLE_END_DATE = 17\n}\ndeclare enum OrganizationSettingLabels {\n    Logo = \"Logo\",\n    TotalAllowEmployee = \"Total Allow Employee\",\n    SalaryDay = \"Salary Day\",\n    Phone = \"Phone No.\",\n    OfficialEmail = \"Official Email\",\n    Website = \"Website\",\n    Address = \"Address\",\n    Description = \"Description\",\n    IncentiveDay = \"Incentive Day\",\n    TDSApplicable = \"TDS Deduction Not Applicable\"\n}\ndeclare enum RoleSettingLabels {\n    LateLogin = \"Late Login (min)\",\n    EarlyLogout = \"Early Logout (min)\",\n    PunchIn = \"Punch In Time\",\n    PunchOut = \"Punch Out Time\",\n    WeekOff = \"Week Off\",\n    Holidays = \"Calendar\",\n    isAdmin = \"Admin?\"\n}\ndeclare enum SalaryStructureCategory {\n    NONPF = 0,\n    PF1 = 1,\n    PF2 = 2\n}\ndeclare enum UpdatedSalaryStructureCategory {\n    NONPF = 0,\n    PF = 1\n}\ndeclare enum GovtSub {\n    ABRY = 0,\n    NotApplicable = 1\n}\ndeclare enum SkillCategory {\n    Unskilled = 0,\n    SemiSkilled = 1,\n    Skilled = 2,\n    HighlySkilled = 3,\n    NotFollowed = 4\n}\ndeclare enum AnnouncementType {\n    Announcement = 0,\n    Birthday = 1,\n    WorkAnniversary = 2,\n    CalendarEvent = 3\n}\ndeclare enum IncentiveType {\n    Monthly = \"Monthly\",\n    Quarterly = \"Quarterly\",\n    Yearly = \"Yearly\",\n    HalfYearly = \"Half yearly\"\n}\ndeclare enum DeductType {\n    Monthly = \"Monthly\",\n    Quarterly = \"Quarterly\",\n    HalfYearly = \"Half Yearly\",\n    Yearly = \"Yearly\"\n}\ndeclare enum AdvanceStatus {\n    Request = \"Request\",\n    Approved = \"Approved\",\n    Pending = \"Pending\",\n    HrApproved = \"Approved By Hr\",\n    Rejected = \"Rejected\",\n    Canceled = \"Canceled\"\n}\n\ndeclare enum RequestStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2,\n    UNPAID = 3,\n    CANCELLED = 4\n}\ndeclare enum RequestType {\n    SICK_LEAVE = 0,\n    CASUAL_LEAVE = 1,\n    WORK_FROM_HOME = 2\n}\ndeclare enum RoleRequestLabel {\n    SICK_LEAVE = \"Sick Leave\",\n    CASUAL_LEAVE = \"Casual Leave\",\n    ANNUAL_LEAVE = \"Annual Leave\",\n    MATERNITY_LEAVE = \"Maternity Leave\",\n    PATERNITY_LEAVE = \"Paternity Leave\",\n    BEREAVEMENT_LEAVE = \"Bereavement Leave\",\n    WORK_FROM_HOME = \"Work From Home\"\n}\n\ndeclare enum emsSupportPriority {\n    High = 0,\n    Medium = 1,\n    Low = 2\n}\ndeclare enum emsSupportType {\n    Request = 0,\n    Incident = 1\n}\ndeclare enum emsSupportCategory {\n    Request = 0\n}\n\ndeclare enum TicketPriority {\n    LOW_PRIORITY = \"LOW PRIORITY\",\n    MEDIUM_PRIORITY = \"MEDIUM PRIORITY\",\n    HIGH_PRIORITY = \"HIGH PRIORITY\"\n}\ndeclare enum TicketDepartment {\n    IT_DEPARTMENT = \"IT DEPARTMENT\",\n    HR_DEPARTMENT = \"HR DEPARTMENT\",\n    FINANCE_DEPARTMENT = \"FINANCE DEPARTMENT\",\n    DEV_DEPARTMENT = \"DEV DEPARTMENT\"\n}\ndeclare enum TicketStatus {\n    OPEN = \"OPEN\",\n    PICKED = \"PICKED\",\n    CLOSED = \"CLOSED\"\n}\ndeclare enum TicketCategory {\n    MANAGER = \"MANAGER\",\n    HR_MANAGEMENT = \"HR_MANAGEMENT\",\n    IT_SUPPORT = \"IT_SUPPORT\"\n}\ndeclare enum TicketUrgency {\n    LOW = 4,\n    MEDIUM = 3,\n    HIGH = 2,\n    URGENT = 1\n}\ndeclare enum TicketImpactCategory {\n    INDIVIDUAL = \"INDIVIDUAL\",\n    TEAM = \"TEAM\",\n    DEPARTMENT = \"DEPARTMENT\"\n}\ndeclare enum DocumentType {\n    PDF = \"PDF\",\n    IMAGE = \"IMAGE\",\n    WORD = \"WORD\",\n    EXCEL = \"EXCEL\",\n    OTHER = \"OTHER\"\n}\n\ndeclare enum EmsAttendanceType {\n    ONLINE = 0,\n    OFFLINE = 1\n}\ndeclare enum EmsUserLeaveStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2\n}\ndeclare enum EmsAttendanceStatus {\n    FULL_DAY = 0,\n    HALF_DAY = 1,\n    ERROR = 2,\n    ABSENT = 3,\n    WEEK_OFF = 4,\n    LEAVE = 5,\n    HOLIDAY = 6,\n    LESS_THAN_HALF_DAY = 7\n}\ndeclare enum EmsUserRequestStatus {\n    APPROVED = 0,\n    PENDING = 1,\n    REJECTED = 2,\n    UNPAID = 3,\n    CANCEL = 4\n}\n\ninterface Identifiable {\n    _id: string | Types.ObjectId;\n}\ninterface ITimeStamp {\n    createdAt?: Date;\n    updatedAt?: Date;\n}\ninterface INameEntity extends Identifiable {\n    sName: string;\n}\ninterface ITagEntity extends INameEntity {\n    sTag?: string;\n}\ninterface ITimeStampIdentifiable extends ITimeStamp, Identifiable {\n}\ninterface IActiveStatus {\n    bIsActive: boolean;\n}\ninterface IStartDateEndDate {\n    dStartDate: Date;\n    dEndDate: Date;\n}\ninterface IConcept extends Document, SchemaTimestampsConfig {\n}\n\ninterface IAccess extends ITagEntity {\n    tRole: string;\n    bCanView: boolean;\n    bCanWrite: boolean;\n}\n\ninterface IAsset {\n    id: number;\n    name: string;\n    imageUrl: string;\n    dateAcquired: Date;\n    status: string;\n}\n\ninterface IAnniversary {\n    user: IUser;\n    anniversaryDate: Date;\n}\ninterface IBirthday {\n    user: IUser;\n    birthdayDate: Date;\n}\n\ninterface IOrganizationBase {\n    /** Tag/identifier for the organization */\n    sTag: string;\n    /** Name of the organization */\n    sName: string;\n    /** Description of the organization */\n    sDescription: string;\n    /** Branch /company name */\n    sBrandName: string;\n    /** Logo URL or path */\n    sLogo: string;\n    /** Organization's email address */\n    sEmail: string;\n}\ninterface IOrganization extends IOrganizationBase, Identifiable {\n}\ninterface IOrganizationModel extends IConcept, IOrganizationBase {\n}\n\ninterface IDepartment {\n    /** Name of the department */\n    sName: string;\n    /** Reference to the department head */\n    tDepartmentHead: string | Types.ObjectId | IUserInfo;\n    /** Reference to the parent organization */\n    tOrganization: string | Types.ObjectId | IOrganization;\n}\ninterface IDepartmentModel extends IConcept, IDepartment {\n}\n\ninterface IDesignationInfoDTO extends ITagEntity {\n}\n\ninterface IHoliday {\n    title: string;\n    date: Date;\n    description: string;\n    image: string;\n}\n\ninterface IPost {\n    title?: string;\n    body: string;\n    user: IUser;\n    date: Date;\n}\n\ninterface IShift extends INameEntity {\n    sTimezone: string;\n    sTime: string;\n    sPunchInTime: string;\n    sPunchOutTime: string;\n    sTimeBuffer: string;\n    isDefault: boolean;\n    tOrganization: IOrganization;\n}\n\ninterface ITodaysAttendance extends IStartDateEndDate {\n    eAttendanceType: AttendanceType;\n    eEndAttendanceType: AttendanceType;\n    tShift: string;\n}\ninterface IAttendanceRequest {\n    organizationId: string;\n    shiftId: string;\n    currentDate: string;\n    departmentId?: string;\n}\ninterface ITodaysAttendanceResponse {\n    dStartDate: Date[];\n    dEndDate: Date[];\n    eAttendanceType: AttendanceType;\n    eEndAttendanceType: AttendanceType;\n    tShift: IShift;\n}\ninterface IAttendanceReport {\n    report: IDayAttendance[];\n}\ninterface IDayAttendance {\n    status: AttendanceStatus;\n    calendarDates: Date | null;\n}\n\ninterface IRequest {\n    dDate: Date[];\n    eStatus: number;\n    eType: string;\n    tSender: string;\n    sReason: string;\n    sMessage: string;\n    tApproverBy: string;\n}\ninterface ILeaveBalance extends Identifiable {\n    sType: RoleRequestLabel;\n    aTotalBalance: 5;\n    aAvailableBalance: 5;\n}\ninterface ILeaveBalanceRequest {\n    organizationId: string;\n    currentYear: number;\n}\n\ninterface IUserDetails extends Identifiable, IActiveStatus {\n    sName: string;\n    tDepartment: IDepartment;\n    dJoinDate: string;\n    tOrganization: IOrganization;\n    tEducationInfo: any[];\n    tExperienceInfo: any[];\n}\ninterface IUserInfo {\n    /** Full name of the user */\n    sName: string;\n    /** Email address of the user */\n    sEmail: string;\n    /** Hashed password of the user */\n    sPassword: string;\n    /** ProfilePicture of the user */\n    sProfilePicture: string;\n    /** Role of the user */\n    tRoleId: string | Types.ObjectId | IRole;\n    /** EmployeeCode of the user */\n    tEmpCode: string;\n    /** Status of the user */\n    eStatus: string;\n    /** Reference to the organization the user belongs to */\n    tOrganizationId: string | Types.ObjectId;\n    /** Reference to the user's role in the system */\n    tDepartmentId: string | Types.ObjectId;\n    /** Reference to the user's department */\n    sDeviceToken?: string;\n}\ninterface IUserInfoModel extends IConcept, IUserInfo {\n}\n\ninterface IRole {\n    /** Name of the role */\n    sName: string;\n    /** Tag/identifier for the role */\n    sTag: string;\n    /** Reference to the head/parent role */\n    tHead: string | Schema.Types.ObjectId | IUserInfo;\n    /** Reference to the organization this role belongs to */\n    tOrganization: string | Schema.Types.ObjectId | IOrganization;\n}\ninterface IRoleModel extends IConcept, IRole {\n}\n\ninterface IUser {\n    id?: number;\n    avatar?: string;\n    image?: string;\n    name?: string;\n    firstName?: string;\n    lastName?: string;\n    username?: string;\n    email?: string;\n    address?: IAddress;\n    phone?: string;\n}\ninterface IAddress {\n    street: string;\n    suite: string;\n    city: string;\n    zipcode: string;\n}\ninterface IUserData extends Identifiable, IActiveStatus {\n    sPassword: string;\n    sEmail: string;\n    aPhoneNumber: number;\n    tBranches: any[];\n    tRole: IRole;\n    bCanLogin: boolean;\n    bOnlyOfficePunch: boolean;\n    tShift: IShift[];\n    bIsApplicableAdvance: boolean;\n    bIsResigned: boolean;\n    bIsCreatedBySuperAdmin: boolean;\n    bIsPermanentWFH: boolean;\n    tIdEmployee: IEmployeeId;\n    tUserDetails: IUserDetails;\n    sProfileUrl?: string;\n}\ninterface IEmployeeId extends IEmployeeCodeInfoDTO {\n    tRole: string;\n}\ninterface IEmployeeCodeInfoDTO extends Identifiable {\n    sCode: string;\n    aPunchId: number;\n}\n\ninterface ILoginResponse {\n    success: boolean;\n    message: string;\n    data: IUserData;\n    headers: {\n        [key: string]: string;\n    };\n}\ninterface IHttpResponse<T> {\n    status: string;\n    statusCode: number;\n    data: T;\n}\n\ninterface IDocument {\n    /** Name of the document */\n    sName: string;\n    /** URL or path to the document */\n    sUrl: string;\n    /** Type of the document */\n    eType: DocumentType;\n    /** Size of the document in bytes */\n    aSize: number;\n    /** MIME type of the document */\n    sMimeType: string;\n    /** Upload date of the document */\n    dUploadDate: Date;\n    /** Reference to the uploader */\n    tUploader: string | Types.ObjectId | IUserInfo;\n}\ninterface IDocumentModel extends IConcept, IDocument {\n}\n\ninterface IAction {\n    icon?: string;\n    label: string;\n    action: () => void;\n}\ninterface ILanguage {\n    name: string;\n    code: string;\n}\ninterface IColumn {\n    field: string;\n    header: string;\n}\ninterface IdentityInfo extends ITagType {\n    title: string;\n    icon?: string;\n    tag?: string;\n    noOfCols: number;\n    values: IdentityInfoValue[];\n}\ninterface IdentityInfoValue {\n    label: string;\n    value: string | number;\n    valueHexColor?: string;\n}\ninterface InfoCardInput {\n    title: string;\n    values: InfoCardValue[];\n}\ninterface InfoCardValue {\n    label: string;\n    value: string | number;\n    valueHexColor?: string;\n}\ninterface ITagType {\n    tagType?: 'success' | 'info' | 'warning' | 'danger' | 'primary' | 'secondary' | 'light' | 'dark';\n}\ninterface IQuickLink {\n    label: string;\n    link: string;\n}\n\ninterface AzadiAttendanceReport {\n    aMonth: number;\n    aTotalDays: number;\n    aWeekends: number;\n    aBusinessDays: number;\n    aHolidays: number;\n    aWorkingDays: number;\n    aLeaves: number;\n    aPresentDay: number;\n}\ninterface AzadiEmployeeReport {\n    aMonth: number;\n    aNewJoin: number;\n    aTotalLeft: number;\n}\n\ninterface IDepartmentDTO extends IDepartment {\n}\n\ninterface IDocumentDTO extends IDocument {\n}\n\ninterface IOrganizationDTO extends IOrganization {\n}\n\ninterface IRoleDTO extends IRole {\n}\n\ninterface ITicketComment {\n    id: number;\n    comment: string;\n    createdAt: Date;\n    createdByUserId: number;\n    ticket: ITicket;\n}\ninterface ITicket extends ITicketBase, Identifiable {\n}\ninterface ITicketBase {\n    /** Reference to the organization */\n    tOrganization: string | Schema.Types.ObjectId | IOrganization;\n    /** User who requested the ticket */\n    tRequester: string | Schema.Types.ObjectId | IUserInfo;\n    /** Current status of the ticket */\n    eStatusKey: TicketStatus;\n    /** Category of the ticket */\n    eCategoryKey: TicketCategory;\n    /** Urgency level of the ticket */\n    eUrgency: TicketUrgency;\n    /** Impact category of the ticket */\n    eImpactCategory: TicketImpactCategory;\n    /** User impacted by the ticket */\n    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n    /** User who approved the ticket */\n    tApprover: string | Schema.Types.ObjectId | IUserInfo;\n    /** User assigned to handle the ticket */\n    tAssignedTo: string | Schema.Types.ObjectId | IUserInfo;\n    /** Observer of the ticket */\n    tObserver: string | Schema.Types.ObjectId | IUserInfo;\n    /** Associated asset with the ticket */\n    tAsset: Record<string, any>;\n    /** Description of the ticket */\n    sDescription: string;\n    /** Title of the ticket */\n    sTitle: string;\n    /** Incident date */\n    dIncidentDate: Date;\n    /** Array of attached documents */\n    aDocuments: IDocument[];\n}\ninterface ITicketModel extends IConcept, ITicketBase {\n}\ninterface ITicketIdentifiable extends IConcept {\n    tTicketId: string | Schema.Types.ObjectId | ITicket;\n}\ninterface ITicketImpactModel extends ITicketIdentifiable {\n    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n}\ninterface ITicketObserverModel extends ITicketIdentifiable {\n    tObserver: string[] | Schema.Types.ObjectId[] | IUserInfo[];\n}\n\ninterface IMessage {\n    sender: IUser;\n    content: string;\n    date: Date;\n    isRead?: boolean;\n    receiver?: IUser;\n}\ninterface INotification {\n    subject: string;\n    message: string;\n}\ninterface IChatModel extends IConcept {\n    tTicketId: string | ITicketModel;\n    tSenderId: string | IUserInfoModel;\n    sContent: string;\n}\n\ninterface IUserInfoDTO extends Omit<IUserInfo, 'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'> {\n    /** Role of the user (as string ID) */\n    tRoleId?: string;\n    /** Organization ID (as string) */\n    tOrganizationId?: string;\n    /** Department ID (as string) */\n    tDepartmentId?: string;\n    /** Password of the user (optional in DTO) */\n    sPassword?: string;\n    /** Employee Code (renamed in DTO) */\n    empCode: string;\n}\n\n/**\n * DTO Interface for Ticket\n */\ninterface ITicketDTO extends Omit<ITicket, 'tRequester' | 'tImpactUser' | 'tApprover' | 'tAssignedTo' | 'tObserver' | 'aDocuments'> {\n    /** User who requested the ticket */\n    tRequester?: string | IUserInfoDTO;\n    /** User impacted by the ticket */\n    tImpactUsers?: string[] | IUserInfoDTO[];\n    /** User who approved the ticket */\n    tApprover?: string | IUserInfoDTO;\n    /** User assigned to handle the ticket */\n    tAssignedTo?: string | IUserInfoDTO;\n    /** Observer(s) of the ticket */\n    tObserver?: string[] | IUserInfoDTO[];\n    /** Array of attached documents */\n    aDocuments?: string[] | IDocumentDTO[];\n}\ninterface IAllTicketDTO {\n    tickets: ITicketDTO[];\n    count: number;\n}\ninterface ITicketReferenceDTO {\n    /** Reference to the ticket */\n    tTicketId: string | ITicketDTO;\n}\ninterface ITicketObserverDTO extends ITicketReferenceDTO {\n    /** Observers of the ticket */\n    tObserver: string[] | IUserInfoDTO[];\n}\ninterface ITicketImpactDTO extends ITicketReferenceDTO {\n    /** Users impacted by the ticket */\n    tImpactUser: string[] | IUserInfo[];\n}\ninterface ICreateTicketTemplateDTO {\n    tOrganization: string;\n    tRequester: string;\n    eStatusKey: string;\n    eCategoryKey: string;\n    eUrgency: string;\n    eImpactCategory: TicketImpactCategory;\n    tImpactUser: string;\n    tApprover: string;\n    tAssignedTo: string;\n    tObserver: string[];\n    tAsset: string;\n    sDescription: string;\n    sTitle: string;\n    dIncidentDate: Date;\n    aDocuments: string[];\n}\ninterface ITicketTemplateDTO {\n    department: string;\n    searchText?: string;\n    endDate?: string;\n    startDate?: string;\n    priority?: TicketSortPriority;\n}\ntype TicketSortPriority = {\n    _id?: 1 | -1;\n    sTitle?: 1 | -1;\n    eUrgency?: 1 | -1;\n    dIncidentDate?: 1 | -1;\n    'tRequester.sName'?: 1 | -1;\n    'tAssignedTo.sName'?: 1 | -1;\n};\ninterface ITicketStatusCountDto {\n    total: number;\n    open: number;\n    closed: number;\n}\n\ndeclare const Ems_CONSTANT_DATA: {\n    ADMIN: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: string;\n        };\n    };\n    USER: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: string;\n        };\n    };\n};\n\nexport { AdvanceStatus, AnnouncementType, AttendanceStatus, AttendanceType, CalendarEventType, DeductType, DocumentType, EmsAttendanceStatus, EmsAttendanceType, EmsUserLeaveStatus, EmsUserRequestStatus, Ems_CONSTANT_DATA, FileType, GovtSub, IncentiveType, InterviewMode, LeaveType, OrganizationSettingLabels, OrganizationSettingType, ProcessType, RequestStatus, RequestType, RoleRequestLabel, RoleSettingLabels, SalaryStructureCategory, SkillCategory, TicketCategory, TicketDepartment, TicketImpactCategory, TicketPriority, TicketStatus, TicketUrgency, UpdatedSalaryStructureCategory, UserAccess, emsSupportCategory, emsSupportPriority, emsSupportType };\nexport type { AzadiAttendanceReport, AzadiEmployeeReport, IAccess, IAction, IActiveStatus, IAllTicketDTO, IAnniversary, IAsset, IAttendanceReport, IAttendanceRequest, IBirthday, IChatModel, IColumn, IConcept, ICreateTicketTemplateDTO, IDayAttendance, IDepartment, IDepartmentDTO, IDepartmentModel, IDesignationInfoDTO, IDocument, IDocumentDTO, IDocumentModel, IEmployeeCodeInfoDTO, IEmployeeId, IHoliday, IHttpResponse, ILanguage, ILeaveBalance, ILeaveBalanceRequest, ILoginResponse, IMessage, INameEntity, INotification, IOrganization, IOrganizationBase, IOrganizationDTO, IOrganizationModel, IPost, IQuickLink, IRequest, IRole, IRoleDTO, IRoleModel, IShift, IStartDateEndDate, ITagEntity, ITagType, ITicket, ITicketComment, ITicketDTO, ITicketImpactDTO, ITicketImpactModel, ITicketModel, ITicketObserverDTO, ITicketObserverModel, ITicketReferenceDTO, ITicketStatusCountDto, ITicketTemplateDTO, ITimeStamp, ITimeStampIdentifiable, ITodaysAttendance, ITodaysAttendanceResponse, IUser, IUserData, IUserDetails, IUserInfo, IUserInfoDTO, IUserInfoModel, Identifiable, IdentityInfo, IdentityInfoValue, InfoCardInput, TicketSortPriority };\n", "map": null, "preliminaryFileName": "index.d.ts", "sourcemapFileName": null}], "fesm2022": [{"exports": ["AdvanceStatus", "AnnouncementType", "AttendanceStatus", "AttendanceType", "CalendarEventType", "DeductType", "DocumentType", "EmsAttendanceStatus", "EmsAttendanceType", "EmsUserLeaveStatus", "EmsUserRequestStatus", "Ems_CONSTANT_DATA", "FileType", "GovtSub", "IncentiveType", "LeaveType", "OrganizationSettingLabels", "OrganizationSettingType", "ProcessType", "RequestStatus", "RequestType", "RoleRequestLabel", "RoleSettingLabels", "SalaryStructureCategory", "SkillCategory", "TicketCategory", "TicketDepartment", "TicketImpactCategory", "TicketPriority", "TicketStatus", "TicketUrgency", "UpdatedSalaryStructureCategory", "UserAccess", "emsSupportCategory", "emsSupportPriority", "emsSupportType"], "facadeModuleId": "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-esm2022\\azaadi-packages.js", "isDynamicEntry": false, "isEntry": true, "isImplicitEntry": false, "moduleIds": ["D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/attendance.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/managements.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/request.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/support.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/ticket.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/users.enum.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/access.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/apiResponse.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/common.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/document.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/frontend.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Dashboard/dashboard.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Dashboard/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/department.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/document.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/organization.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/role.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/ticket.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/userInfo.dto.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/calendar-event.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/department.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/designation.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/holiday.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/organization.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/post.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/role.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/shift.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/message.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/ticket.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/assets.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/attendance.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/request.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/user.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/userDetails.interface.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/index.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/utils/constants.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/admin-public-api.js", "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/public-api.js", "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-esm2022\\azaadi-packages.js"], "name": "azaadi-packages", "type": "chunk", "dynamicImports": [], "fileName": "azaadi-packages.mjs", "implicitlyLoadedBefore": [], "importedBindings": {}, "imports": [], "modules": {"D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/attendance.enum.js": {"code": "var AttendanceStatus;\n(function (AttendanceStatus) {\n    AttendanceStatus[\"PRESENT\"] = \"Present\";\n    AttendanceStatus[\"Leave\"] = \"Leave\";\n    AttendanceStatus[\"PUBLIC_HOLIDAY\"] = \"Public Holiday\";\n    AttendanceStatus[\"ORGANIZATION_WEEKEND\"] = \"Organization Weekend\";\n    AttendanceStatus[\"ERROR\"] = \"Error\";\n    AttendanceStatus[\"TODAYS_DATE\"] = \"Today's Date\";\n    AttendanceStatus[\"LHD\"] = \"Less Than Half Day\";\n    AttendanceStatus[\"HD\"] = \"Half Day\";\n})(AttendanceStatus || (AttendanceStatus = {}));\nvar AttendanceType;\n(function (AttendanceType) {\n    AttendanceType[AttendanceType[\"ONLINE\"] = 0] = \"ONLINE\";\n    AttendanceType[AttendanceType[\"OFFLINE\"] = 1] = \"OFFLINE\";\n})(AttendanceType || (AttendanceType = {}));", "originalLength": 781, "removedExports": [], "renderedExports": ["AttendanceStatus", "AttendanceType"], "renderedLength": 723}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/managements.enum.js": {"code": "var UserAccess;\n(function (UserAccess) {\n    UserAccess[UserAccess[\"READ\"] = 0] = \"READ\";\n    UserAccess[UserAccess[\"WRITE\"] = 1] = \"WRITE\";\n    UserAccess[UserAccess[\"ALL\"] = 2] = \"ALL\";\n})(UserAccess || (UserAccess = {}));\nvar FileType;\n(function (FileType) {\n    FileType[FileType[\"pdf\"] = 0] = \"pdf\";\n    FileType[FileType[\"jpg\"] = 1] = \"jpg\";\n    FileType[FileType[\"jpeg\"] = 2] = \"jpeg\";\n    FileType[FileType[\"doc\"] = 3] = \"doc\";\n    FileType[FileType[\"excel\"] = 4] = \"excel\";\n    FileType[FileType[\"xml\"] = 5] = \"xml\";\n})(FileType || (FileType = {}));\nvar ProcessType;\n(function (ProcessType) {\n    ProcessType[ProcessType[\"EXAM\"] = 0] = \"EXAM\";\n    ProcessType[ProcessType[\"INTERVIEW\"] = 1] = \"INTERVIEW\";\n    ProcessType[ProcessType[\"DOCUMENT\"] = 2] = \"DOCUMENT\";\n    ProcessType[ProcessType[\"BACKGROUND\"] = 3] = \"BACKGROUND\";\n})(ProcessType || (ProcessType = {}));\nvar LeaveType;\n(function (LeaveType) {\n    LeaveType[LeaveType[\"EXAM\"] = 0] = \"EXAM\";\n    LeaveType[LeaveType[\"PERSONAL\"] = 1] = \"PERSONAL\";\n    LeaveType[LeaveType[\"SICK\"] = 2] = \"SICK\";\n    LeaveType[LeaveType[\"UNPAID\"] = 3] = \"UNPAID\";\n    LeaveType[LeaveType[\"OTHERS\"] = 4] = \"OTHERS\";\n})(LeaveType || (LeaveType = {}));\nvar CalendarEventType;\n(function (CalendarEventType) {\n    CalendarEventType[CalendarEventType[\"HOLIDAY\"] = 0] = \"HOLIDAY\";\n    CalendarEventType[CalendarEventType[\"WEEKOFF\"] = 1] = \"WEEKOFF\";\n    CalendarEventType[CalendarEventType[\"EVENT\"] = 2] = \"EVENT\";\n})(CalendarEventType || (CalendarEventType = {}));\nvar OrganizationSettingType;\n(function (OrganizationSettingType) {\n    OrganizationSettingType[OrganizationSettingType[\"SICK_LEAVE\"] = 0] = \"SICK_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"PERSONAL_LEAVE\"] = 1] = \"PERSONAL_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"EXAM_LEAVE\"] = 2] = \"EXAM_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"OTHER_LEAVE\"] = 3] = \"OTHER_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"UNPAID_LEAVE\"] = 4] = \"UNPAID_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"WORKING_HOUR\"] = 5] = \"WORKING_HOUR\";\n    OrganizationSettingType[OrganizationSettingType[\"DESCRIPTION\"] = 6] = \"DESCRIPTION\";\n    OrganizationSettingType[OrganizationSettingType[\"BRAND_NAME\"] = 7] = \"BRAND_NAME\";\n    OrganizationSettingType[OrganizationSettingType[\"LOGO\"] = 8] = \"LOGO\";\n    OrganizationSettingType[OrganizationSettingType[\"PHONE\"] = 9] = \"PHONE\";\n    OrganizationSettingType[OrganizationSettingType[\"ADDRESS\"] = 10] = \"ADDRESS\";\n    OrganizationSettingType[OrganizationSettingType[\"TOTAL_EMPLOYEE\"] = 11] = \"TOTAL_EMPLOYEE\";\n    OrganizationSettingType[OrganizationSettingType[\"HOLIDAY\"] = 12] = \"HOLIDAY\";\n    OrganizationSettingType[OrganizationSettingType[\"WEEKOFF\"] = 13] = \"WEEKOFF\";\n    OrganizationSettingType[OrganizationSettingType[\"SIGN_IN\"] = 14] = \"SIGN_IN\";\n    OrganizationSettingType[OrganizationSettingType[\"SIGN_OUT\"] = 15] = \"SIGN_OUT\";\n    OrganizationSettingType[OrganizationSettingType[\"SALARY_CIRCLE_START_DATE\"] = 16] = \"SALARY_CIRCLE_START_DATE\";\n    OrganizationSettingType[OrganizationSettingType[\"SALARY_CIRCLE_END_DATE\"] = 17] = \"SALARY_CIRCLE_END_DATE\";\n})(OrganizationSettingType || (OrganizationSettingType = {}));\nvar OrganizationSettingLabels;\n(function (OrganizationSettingLabels) {\n    OrganizationSettingLabels[\"Logo\"] = \"Logo\";\n    OrganizationSettingLabels[\"TotalAllowEmployee\"] = \"Total Allow Employee\";\n    OrganizationSettingLabels[\"SalaryDay\"] = \"Salary Day\";\n    OrganizationSettingLabels[\"Phone\"] = \"Phone No.\";\n    OrganizationSettingLabels[\"OfficialEmail\"] = \"Official Email\";\n    OrganizationSettingLabels[\"Website\"] = \"Website\";\n    OrganizationSettingLabels[\"Address\"] = \"Address\";\n    OrganizationSettingLabels[\"Description\"] = \"Description\";\n    OrganizationSettingLabels[\"IncentiveDay\"] = \"Incentive Day\";\n    OrganizationSettingLabels[\"TDSApplicable\"] = \"TDS Deduction Not Applicable\";\n})(OrganizationSettingLabels || (OrganizationSettingLabels = {}));\nvar RoleSettingLabels;\n(function (RoleSettingLabels) {\n    RoleSettingLabels[\"LateLogin\"] = \"Late Login (min)\";\n    RoleSettingLabels[\"EarlyLogout\"] = \"Early Logout (min)\";\n    RoleSettingLabels[\"PunchIn\"] = \"Punch In Time\";\n    RoleSettingLabels[\"PunchOut\"] = \"Punch Out Time\";\n    RoleSettingLabels[\"WeekOff\"] = \"Week Off\";\n    RoleSettingLabels[\"Holidays\"] = \"Calendar\";\n    RoleSettingLabels[\"isAdmin\"] = \"Admin?\";\n})(RoleSettingLabels || (RoleSettingLabels = {}));\nvar SalaryStructureCategory;\n(function (SalaryStructureCategory) {\n    SalaryStructureCategory[SalaryStructureCategory[\"NONPF\"] = 0] = \"NONPF\";\n    SalaryStructureCategory[SalaryStructureCategory[\"PF1\"] = 1] = \"PF1\";\n    SalaryStructureCategory[SalaryStructureCategory[\"PF2\"] = 2] = \"PF2\";\n})(SalaryStructureCategory || (SalaryStructureCategory = {}));\nvar UpdatedSalaryStructureCategory;\n(function (UpdatedSalaryStructureCategory) {\n    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory[\"NONPF\"] = 0] = \"NONPF\";\n    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory[\"PF\"] = 1] = \"PF\";\n    // PFESI = 2,\n})(UpdatedSalaryStructureCategory || (UpdatedSalaryStructureCategory = {}));\nvar GovtSub;\n(function (GovtSub) {\n    GovtSub[GovtSub[\"ABRY\"] = 0] = \"ABRY\";\n    GovtSub[GovtSub[\"NotApplicable\"] = 1] = \"NotApplicable\";\n})(GovtSub || (GovtSub = {}));\nvar SkillCategory;\n(function (SkillCategory) {\n    SkillCategory[SkillCategory[\"Unskilled\"] = 0] = \"Unskilled\";\n    SkillCategory[SkillCategory[\"SemiSkilled\"] = 1] = \"SemiSkilled\";\n    SkillCategory[SkillCategory[\"Skilled\"] = 2] = \"Skilled\";\n    SkillCategory[SkillCategory[\"HighlySkilled\"] = 3] = \"HighlySkilled\";\n    SkillCategory[SkillCategory[\"NotFollowed\"] = 4] = \"NotFollowed\";\n})(SkillCategory || (SkillCategory = {}));\nvar AnnouncementType;\n(function (AnnouncementType) {\n    AnnouncementType[AnnouncementType[\"Announcement\"] = 0] = \"Announcement\";\n    AnnouncementType[AnnouncementType[\"Birthday\"] = 1] = \"Birthday\";\n    AnnouncementType[AnnouncementType[\"WorkAnniversary\"] = 2] = \"WorkAnniversary\";\n    AnnouncementType[AnnouncementType[\"CalendarEvent\"] = 3] = \"CalendarEvent\";\n})(AnnouncementType || (AnnouncementType = {}));\nvar IncentiveType;\n(function (IncentiveType) {\n    IncentiveType[\"Monthly\"] = \"Monthly\";\n    IncentiveType[\"Quarterly\"] = \"Quarterly\";\n    IncentiveType[\"Yearly\"] = \"Yearly\";\n    IncentiveType[\"HalfYearly\"] = \"Half yearly\";\n})(IncentiveType || (IncentiveType = {}));\nvar DeductType;\n(function (DeductType) {\n    DeductType[\"Monthly\"] = \"Monthly\";\n    DeductType[\"Quarterly\"] = \"Quarterly\";\n    DeductType[\"HalfYearly\"] = \"Half Yearly\";\n    DeductType[\"Yearly\"] = \"Yearly\";\n})(DeductType || (DeductType = {}));\nvar AdvanceStatus;\n(function (AdvanceStatus) {\n    AdvanceStatus[\"Request\"] = \"Request\";\n    AdvanceStatus[\"Approved\"] = \"Approved\";\n    AdvanceStatus[\"Pending\"] = \"Pending\";\n    AdvanceStatus[\"HrApproved\"] = \"Approved By Hr\";\n    AdvanceStatus[\"Rejected\"] = \"Rejected\";\n    AdvanceStatus[\"Canceled\"] = \"Canceled\";\n})(AdvanceStatus || (AdvanceStatus = {}));", "originalLength": 7209, "removedExports": [], "renderedExports": ["UserAccess", "FileType", "ProcessType", "LeaveType", "CalendarEventType", "OrganizationSettingType", "OrganizationSettingLabels", "RoleSettingLabels", "SalaryStructureCategory", "UpdatedSalaryStructureCategory", "GovtSub", "SkillCategory", "AnnouncementType", "IncentiveType", "DeductType", "AdvanceStatus"], "renderedLength": 7052}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/request.enum.js": {"code": "var RequestStatus;\n(function (RequestStatus) {\n    RequestStatus[RequestStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    RequestStatus[RequestStatus[\"PENDING\"] = 1] = \"PENDING\";\n    RequestStatus[RequestStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n    RequestStatus[RequestStatus[\"UNPAID\"] = 3] = \"UNPAID\";\n    RequestStatus[RequestStatus[\"CANCELLED\"] = 4] = \"CANCELLED\";\n})(RequestStatus || (RequestStatus = {}));\nvar RequestType;\n(function (RequestType) {\n    RequestType[RequestType[\"SICK_LEAVE\"] = 0] = \"SICK_LEAVE\";\n    RequestType[RequestType[\"CASUAL_LEAVE\"] = 1] = \"CASUAL_LEAVE\";\n    RequestType[RequestType[\"WORK_FROM_HOME\"] = 2] = \"WORK_FROM_HOME\";\n})(RequestType || (RequestType = {}));\nvar RoleRequestLabel;\n(function (RoleRequestLabel) {\n    RoleRequestLabel[\"SICK_LEAVE\"] = \"Sick Leave\";\n    RoleRequestLabel[\"CASUAL_LEAVE\"] = \"Casual Leave\";\n    RoleRequestLabel[\"ANNUAL_LEAVE\"] = \"Annual Leave\";\n    RoleRequestLabel[\"MATERNITY_LEAVE\"] = \"Maternity Leave\";\n    RoleRequestLabel[\"PATERNITY_LEAVE\"] = \"Paternity Leave\";\n    RoleRequestLabel[\"BEREAVEMENT_LEAVE\"] = \"Bereavement Leave\";\n    RoleRequestLabel[\"WORK_FROM_HOME\"] = \"Work From Home\";\n})(RoleRequestLabel || (RoleRequestLabel = {}));", "originalLength": 1254, "removedExports": [], "renderedExports": ["RequestStatus", "RequestType", "RoleRequestLabel"], "renderedLength": 1192}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/support.enum.js": {"code": "var emsSupportPriority;\n(function (emsSupportPriority) {\n    emsSupportPriority[emsSupportPriority[\"High\"] = 0] = \"High\";\n    emsSupportPriority[emsSupportPriority[\"Medium\"] = 1] = \"Medium\";\n    emsSupportPriority[emsSupportPriority[\"Low\"] = 2] = \"Low\";\n})(emsSupportPriority || (emsSupportPriority = {}));\nvar emsSupportType;\n(function (emsSupportType) {\n    emsSupportType[emsSupportType[\"Request\"] = 0] = \"Request\";\n    emsSupportType[emsSupportType[\"Incident\"] = 1] = \"Incident\";\n})(emsSupportType || (emsSupportType = {}));\nvar emsSupportCategory;\n(function (emsSupportCategory) {\n    emsSupportCategory[emsSupportCategory[\"Request\"] = 0] = \"Request\";\n})(emsSupportCategory || (emsSupportCategory = {}));", "originalLength": 771, "removedExports": [], "renderedExports": ["emsSupportPriority", "emsSupportType", "emsSupportCategory"], "renderedLength": 709}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/ticket.enum.js": {"code": "var TicketPriority;\n(function (TicketPriority) {\n    TicketPriority[\"LOW_PRIORITY\"] = \"LOW PRIORITY\";\n    TicketPriority[\"MEDIUM_PRIORITY\"] = \"MEDIUM PRIORITY\";\n    TicketPriority[\"HIGH_PRIORITY\"] = \"HIGH PRIORITY\";\n})(TicketPriority || (TicketPriority = {}));\nvar TicketDepartment;\n(function (TicketDepartment) {\n    TicketDepartment[\"IT_DEPARTMENT\"] = \"IT DEPARTMENT\";\n    TicketDepartment[\"HR_DEPARTMENT\"] = \"HR DEPARTMENT\";\n    TicketDepartment[\"FINANCE_DEPARTMENT\"] = \"FINANCE DEPARTMENT\";\n    TicketDepartment[\"DEV_DEPARTMENT\"] = \"DEV DEPARTMENT\";\n})(TicketDepartment || (TicketDepartment = {}));\nvar TicketStatus;\n(function (TicketStatus) {\n    TicketStatus[\"OPEN\"] = \"OPEN\";\n    TicketStatus[\"PICKED\"] = \"PICKED\";\n    TicketStatus[\"CLOSED\"] = \"CLOSED\";\n})(TicketStatus || (TicketStatus = {}));\nvar TicketCategory;\n(function (TicketCategory) {\n    TicketCategory[\"MANAGER\"] = \"MANAGER\";\n    TicketCategory[\"HR_MANAGEMENT\"] = \"HR_MANAGEMENT\";\n    TicketCategory[\"IT_SUPPORT\"] = \"IT_SUPPORT\";\n})(TicketCategory || (TicketCategory = {}));\nvar TicketUrgency;\n(function (TicketUrgency) {\n    TicketUrgency[TicketUrgency[\"LOW\"] = 4] = \"LOW\";\n    TicketUrgency[TicketUrgency[\"MEDIUM\"] = 3] = \"MEDIUM\";\n    TicketUrgency[TicketUrgency[\"HIGH\"] = 2] = \"HIGH\";\n    TicketUrgency[TicketUrgency[\"URGENT\"] = 1] = \"URGENT\";\n})(TicketUrgency || (TicketUrgency = {}));\nvar TicketImpactCategory;\n(function (TicketImpactCategory) {\n    TicketImpactCategory[\"INDIVIDUAL\"] = \"INDIVIDUAL\";\n    TicketImpactCategory[\"TEAM\"] = \"TEAM\";\n    TicketImpactCategory[\"DEPARTMENT\"] = \"DEPARTMENT\";\n})(TicketImpactCategory || (TicketImpactCategory = {}));\nvar DocumentType;\n(function (DocumentType) {\n    DocumentType[\"PDF\"] = \"PDF\";\n    DocumentType[\"IMAGE\"] = \"IMAGE\";\n    DocumentType[\"WORD\"] = \"WORD\";\n    DocumentType[\"EXCEL\"] = \"EXCEL\";\n    DocumentType[\"OTHER\"] = \"OTHER\";\n})(DocumentType || (DocumentType = {}));", "originalLength": 1983, "removedExports": [], "renderedExports": ["TicketPriority", "TicketDepartment", "TicketStatus", "TicketCategory", "TicketUrgency", "TicketImpactCategory", "DocumentType"], "renderedLength": 1894}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/users.enum.js": {"code": "var EmsAttendanceType;\n(function (EmsAttendanceType) {\n    EmsAttendanceType[EmsAttendanceType[\"ONLINE\"] = 0] = \"ONLINE\";\n    EmsAttendanceType[EmsAttendanceType[\"OFFLINE\"] = 1] = \"OFFLINE\";\n})(EmsAttendanceType || (EmsAttendanceType = {}));\nvar EmsUserLeaveStatus;\n(function (EmsUserLeaveStatus) {\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"PENDING\"] = 1] = \"PENDING\";\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n})(EmsUserLeaveStatus || (EmsUserLeaveStatus = {}));\nvar EmsAttendanceStatus;\n(function (EmsAttendanceStatus) {\n    EmsAttendanceStatus[EmsAttendanceStatus[\"FULL_DAY\"] = 0] = \"FULL_DAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"HALF_DAY\"] = 1] = \"HALF_DAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"ERROR\"] = 2] = \"ERROR\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"ABSENT\"] = 3] = \"ABSENT\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"WEEK_OFF\"] = 4] = \"WEEK_OFF\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"LEAVE\"] = 5] = \"LEAVE\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"HOLIDAY\"] = 6] = \"HOLIDAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"LESS_THAN_HALF_DAY\"] = 7] = \"LESS_THAN_HALF_DAY\";\n})(EmsAttendanceStatus || (EmsAttendanceStatus = {}));\n;\nvar EmsUserRequestStatus;\n(function (EmsUserRequestStatus) {\n    EmsUserRequestStatus[EmsUserRequestStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"PENDING\"] = 1] = \"PENDING\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"UNPAID\"] = 3] = \"UNPAID\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"CANCEL\"] = 4] = \"CANCEL\";\n})(EmsUserRequestStatus || (EmsUserRequestStatus = {}));\n;", "originalLength": 1848, "removedExports": [], "renderedExports": ["EmsAttendanceType", "EmsUserLeaveStatus", "EmsAttendanceStatus", "EmsUserRequestStatus"], "renderedLength": 1781}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/enums/index.js": {"code": "", "originalLength": 229, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/access.interface.js": {"code": "", "originalLength": 55, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/apiResponse.interface.js": {"code": "", "originalLength": 60, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/common.interface.js": {"code": "", "originalLength": 55, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/document.interface.js": {"code": "", "originalLength": 57, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/frontend.interface.js": {"code": "", "originalLength": 57, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Common/index.js": {"code": "", "originalLength": 222, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Dashboard/dashboard.interface.js": {"code": "", "originalLength": 58, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Dashboard/index.js": {"code": "", "originalLength": 72, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/department.dto.interface.js": {"code": "", "originalLength": 63, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/document.dto.interface.js": {"code": "", "originalLength": 61, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/organization.dto.interface.js": {"code": "", "originalLength": 65, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/role.dto.interface.js": {"code": "", "originalLength": 57, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/ticket.dto.interface.js": {"code": "", "originalLength": 59, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/userInfo.dto.interface.js": {"code": "// export interface IUserListDTO extends IEmployeeCodeInfoDTO {\n//   sEmail: string;\n//   sCode: string;\n//   aPhoneNumber: number;\n//   sName: string;\n//   sProfileUrl?: string;\n//   tOrganization: IOrganization;\n//   dJoiningDate: Date;\n//   tRole: IRole;\n//   bIsActive: boolean;\n// }", "originalLength": 349, "removedExports": [], "renderedExports": [], "renderedLength": 287}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/DTO/index.js": {"code": "", "originalLength": 285, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/calendar-event.interface.js": {"code": "", "originalLength": 63, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/department.interface.js": {"code": "", "originalLength": 59, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/designation.interface.js": {"code": "", "originalLength": 60, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/holiday.interface.js": {"code": "", "originalLength": 56, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/organization.interface.js": {"code": "", "originalLength": 61, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/post.interface.js": {"code": "", "originalLength": 53, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/role.interface.js": {"code": "", "originalLength": 53, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/shift.interface.js": {"code": "", "originalLength": 54, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Organization/index.js": {"code": "", "originalLength": 340, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/message.interface.js": {"code": "", "originalLength": 56, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/ticket.interface.js": {"code": "", "originalLength": 55, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/Ticket/index.js": {"code": "", "originalLength": 106, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/assets.interface.js": {"code": "", "originalLength": 55, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/attendance.interface.js": {"code": "", "originalLength": 59, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/request.interface.js": {"code": "", "originalLength": 56, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/user.interface.js": {"code": "", "originalLength": 53, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/userDetails.interface.js": {"code": "", "originalLength": 60, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/interface/User/index.js": {"code": "", "originalLength": 221, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/lib/utils/constants.js": {"code": "const Ems_CONSTANT_DATA = {\n    ADMIN: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: 'fdfd dfdf',\n        },\n    },\n    USER: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: 'fdfd dfdf',\n        },\n    },\n};", "originalLength": 252, "removedExports": [], "renderedExports": ["Ems_CONSTANT_DATA"], "renderedLength": 207}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/Admin/admin-public-api.js": {"code": "", "originalLength": 392, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:/GitCodeInternal/azaadi/packages/azaadi-packages/dist/azaadi-packages/tmp-esm2022/public-api.js": {"code": "", "originalLength": 80, "removedExports": [], "renderedExports": [], "renderedLength": 0}, "D:\\GitCodeInternal\\azaadi\\packages\\azaadi-packages\\dist\\azaadi-packages\\tmp-esm2022\\azaadi-packages.js": {"code": "/**\n * Generated bundle index. Do not edit.\n */", "originalLength": 121, "removedExports": [], "renderedExports": [], "renderedLength": 47}}, "referencedFiles": [], "code": "var AttendanceStatus;\n(function (AttendanceStatus) {\n    AttendanceStatus[\"PRESENT\"] = \"Present\";\n    AttendanceStatus[\"Leave\"] = \"Leave\";\n    AttendanceStatus[\"PUBLIC_HOLIDAY\"] = \"Public Holiday\";\n    AttendanceStatus[\"ORGANIZATION_WEEKEND\"] = \"Organization Weekend\";\n    AttendanceStatus[\"ERROR\"] = \"Error\";\n    AttendanceStatus[\"TODAYS_DATE\"] = \"Today's Date\";\n    AttendanceStatus[\"LHD\"] = \"Less Than Half Day\";\n    AttendanceStatus[\"HD\"] = \"Half Day\";\n})(AttendanceStatus || (AttendanceStatus = {}));\nvar AttendanceType;\n(function (AttendanceType) {\n    AttendanceType[AttendanceType[\"ONLINE\"] = 0] = \"ONLINE\";\n    AttendanceType[AttendanceType[\"OFFLINE\"] = 1] = \"OFFLINE\";\n})(AttendanceType || (AttendanceType = {}));\n\nvar UserAccess;\n(function (UserAccess) {\n    UserAccess[UserAccess[\"READ\"] = 0] = \"READ\";\n    UserAccess[UserAccess[\"WRITE\"] = 1] = \"WRITE\";\n    UserAccess[UserAccess[\"ALL\"] = 2] = \"ALL\";\n})(UserAccess || (UserAccess = {}));\nvar FileType;\n(function (FileType) {\n    FileType[FileType[\"pdf\"] = 0] = \"pdf\";\n    FileType[FileType[\"jpg\"] = 1] = \"jpg\";\n    FileType[FileType[\"jpeg\"] = 2] = \"jpeg\";\n    FileType[FileType[\"doc\"] = 3] = \"doc\";\n    FileType[FileType[\"excel\"] = 4] = \"excel\";\n    FileType[FileType[\"xml\"] = 5] = \"xml\";\n})(FileType || (FileType = {}));\nvar ProcessType;\n(function (ProcessType) {\n    ProcessType[ProcessType[\"EXAM\"] = 0] = \"EXAM\";\n    ProcessType[ProcessType[\"INTERVIEW\"] = 1] = \"INTERVIEW\";\n    ProcessType[ProcessType[\"DOCUMENT\"] = 2] = \"DOCUMENT\";\n    ProcessType[ProcessType[\"BACKGROUND\"] = 3] = \"BACKGROUND\";\n})(ProcessType || (ProcessType = {}));\nvar LeaveType;\n(function (LeaveType) {\n    LeaveType[LeaveType[\"EXAM\"] = 0] = \"EXAM\";\n    LeaveType[LeaveType[\"PERSONAL\"] = 1] = \"PERSONAL\";\n    LeaveType[LeaveType[\"SICK\"] = 2] = \"SICK\";\n    LeaveType[LeaveType[\"UNPAID\"] = 3] = \"UNPAID\";\n    LeaveType[LeaveType[\"OTHERS\"] = 4] = \"OTHERS\";\n})(LeaveType || (LeaveType = {}));\nvar CalendarEventType;\n(function (CalendarEventType) {\n    CalendarEventType[CalendarEventType[\"HOLIDAY\"] = 0] = \"HOLIDAY\";\n    CalendarEventType[CalendarEventType[\"WEEKOFF\"] = 1] = \"WEEKOFF\";\n    CalendarEventType[CalendarEventType[\"EVENT\"] = 2] = \"EVENT\";\n})(CalendarEventType || (CalendarEventType = {}));\nvar OrganizationSettingType;\n(function (OrganizationSettingType) {\n    OrganizationSettingType[OrganizationSettingType[\"SICK_LEAVE\"] = 0] = \"SICK_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"PERSONAL_LEAVE\"] = 1] = \"PERSONAL_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"EXAM_LEAVE\"] = 2] = \"EXAM_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"OTHER_LEAVE\"] = 3] = \"OTHER_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"UNPAID_LEAVE\"] = 4] = \"UNPAID_LEAVE\";\n    OrganizationSettingType[OrganizationSettingType[\"WORKING_HOUR\"] = 5] = \"WORKING_HOUR\";\n    OrganizationSettingType[OrganizationSettingType[\"DESCRIPTION\"] = 6] = \"DESCRIPTION\";\n    OrganizationSettingType[OrganizationSettingType[\"BRAND_NAME\"] = 7] = \"BRAND_NAME\";\n    OrganizationSettingType[OrganizationSettingType[\"LOGO\"] = 8] = \"LOGO\";\n    OrganizationSettingType[OrganizationSettingType[\"PHONE\"] = 9] = \"PHONE\";\n    OrganizationSettingType[OrganizationSettingType[\"ADDRESS\"] = 10] = \"ADDRESS\";\n    OrganizationSettingType[OrganizationSettingType[\"TOTAL_EMPLOYEE\"] = 11] = \"TOTAL_EMPLOYEE\";\n    OrganizationSettingType[OrganizationSettingType[\"HOLIDAY\"] = 12] = \"HOLIDAY\";\n    OrganizationSettingType[OrganizationSettingType[\"WEEKOFF\"] = 13] = \"WEEKOFF\";\n    OrganizationSettingType[OrganizationSettingType[\"SIGN_IN\"] = 14] = \"SIGN_IN\";\n    OrganizationSettingType[OrganizationSettingType[\"SIGN_OUT\"] = 15] = \"SIGN_OUT\";\n    OrganizationSettingType[OrganizationSettingType[\"SALARY_CIRCLE_START_DATE\"] = 16] = \"SALARY_CIRCLE_START_DATE\";\n    OrganizationSettingType[OrganizationSettingType[\"SALARY_CIRCLE_END_DATE\"] = 17] = \"SALARY_CIRCLE_END_DATE\";\n})(OrganizationSettingType || (OrganizationSettingType = {}));\nvar OrganizationSettingLabels;\n(function (OrganizationSettingLabels) {\n    OrganizationSettingLabels[\"Logo\"] = \"Logo\";\n    OrganizationSettingLabels[\"TotalAllowEmployee\"] = \"Total Allow Employee\";\n    OrganizationSettingLabels[\"SalaryDay\"] = \"Salary Day\";\n    OrganizationSettingLabels[\"Phone\"] = \"Phone No.\";\n    OrganizationSettingLabels[\"OfficialEmail\"] = \"Official Email\";\n    OrganizationSettingLabels[\"Website\"] = \"Website\";\n    OrganizationSettingLabels[\"Address\"] = \"Address\";\n    OrganizationSettingLabels[\"Description\"] = \"Description\";\n    OrganizationSettingLabels[\"IncentiveDay\"] = \"Incentive Day\";\n    OrganizationSettingLabels[\"TDSApplicable\"] = \"TDS Deduction Not Applicable\";\n})(OrganizationSettingLabels || (OrganizationSettingLabels = {}));\nvar RoleSettingLabels;\n(function (RoleSettingLabels) {\n    RoleSettingLabels[\"LateLogin\"] = \"Late Login (min)\";\n    RoleSettingLabels[\"EarlyLogout\"] = \"Early Logout (min)\";\n    RoleSettingLabels[\"PunchIn\"] = \"Punch In Time\";\n    RoleSettingLabels[\"PunchOut\"] = \"Punch Out Time\";\n    RoleSettingLabels[\"WeekOff\"] = \"Week Off\";\n    RoleSettingLabels[\"Holidays\"] = \"Calendar\";\n    RoleSettingLabels[\"isAdmin\"] = \"Admin?\";\n})(RoleSettingLabels || (RoleSettingLabels = {}));\nvar SalaryStructureCategory;\n(function (SalaryStructureCategory) {\n    SalaryStructureCategory[SalaryStructureCategory[\"NONPF\"] = 0] = \"NONPF\";\n    SalaryStructureCategory[SalaryStructureCategory[\"PF1\"] = 1] = \"PF1\";\n    SalaryStructureCategory[SalaryStructureCategory[\"PF2\"] = 2] = \"PF2\";\n})(SalaryStructureCategory || (SalaryStructureCategory = {}));\nvar UpdatedSalaryStructureCategory;\n(function (UpdatedSalaryStructureCategory) {\n    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory[\"NONPF\"] = 0] = \"NONPF\";\n    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory[\"PF\"] = 1] = \"PF\";\n    // PFESI = 2,\n})(UpdatedSalaryStructureCategory || (UpdatedSalaryStructureCategory = {}));\nvar GovtSub;\n(function (GovtSub) {\n    GovtSub[GovtSub[\"ABRY\"] = 0] = \"ABRY\";\n    GovtSub[GovtSub[\"NotApplicable\"] = 1] = \"NotApplicable\";\n})(GovtSub || (GovtSub = {}));\nvar SkillCategory;\n(function (SkillCategory) {\n    SkillCategory[SkillCategory[\"Unskilled\"] = 0] = \"Unskilled\";\n    SkillCategory[SkillCategory[\"SemiSkilled\"] = 1] = \"SemiSkilled\";\n    SkillCategory[SkillCategory[\"Skilled\"] = 2] = \"Skilled\";\n    SkillCategory[SkillCategory[\"HighlySkilled\"] = 3] = \"HighlySkilled\";\n    SkillCategory[SkillCategory[\"NotFollowed\"] = 4] = \"NotFollowed\";\n})(SkillCategory || (SkillCategory = {}));\nvar AnnouncementType;\n(function (AnnouncementType) {\n    AnnouncementType[AnnouncementType[\"Announcement\"] = 0] = \"Announcement\";\n    AnnouncementType[AnnouncementType[\"Birthday\"] = 1] = \"Birthday\";\n    AnnouncementType[AnnouncementType[\"WorkAnniversary\"] = 2] = \"WorkAnniversary\";\n    AnnouncementType[AnnouncementType[\"CalendarEvent\"] = 3] = \"CalendarEvent\";\n})(AnnouncementType || (AnnouncementType = {}));\nvar IncentiveType;\n(function (IncentiveType) {\n    IncentiveType[\"Monthly\"] = \"Monthly\";\n    IncentiveType[\"Quarterly\"] = \"Quarterly\";\n    IncentiveType[\"Yearly\"] = \"Yearly\";\n    IncentiveType[\"HalfYearly\"] = \"Half yearly\";\n})(IncentiveType || (IncentiveType = {}));\nvar DeductType;\n(function (DeductType) {\n    DeductType[\"Monthly\"] = \"Monthly\";\n    DeductType[\"Quarterly\"] = \"Quarterly\";\n    DeductType[\"HalfYearly\"] = \"Half Yearly\";\n    DeductType[\"Yearly\"] = \"Yearly\";\n})(DeductType || (DeductType = {}));\nvar AdvanceStatus;\n(function (AdvanceStatus) {\n    AdvanceStatus[\"Request\"] = \"Request\";\n    AdvanceStatus[\"Approved\"] = \"Approved\";\n    AdvanceStatus[\"Pending\"] = \"Pending\";\n    AdvanceStatus[\"HrApproved\"] = \"Approved By Hr\";\n    AdvanceStatus[\"Rejected\"] = \"Rejected\";\n    AdvanceStatus[\"Canceled\"] = \"Canceled\";\n})(AdvanceStatus || (AdvanceStatus = {}));\n\nvar RequestStatus;\n(function (RequestStatus) {\n    RequestStatus[RequestStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    RequestStatus[RequestStatus[\"PENDING\"] = 1] = \"PENDING\";\n    RequestStatus[RequestStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n    RequestStatus[RequestStatus[\"UNPAID\"] = 3] = \"UNPAID\";\n    RequestStatus[RequestStatus[\"CANCELLED\"] = 4] = \"CANCELLED\";\n})(RequestStatus || (RequestStatus = {}));\nvar RequestType;\n(function (RequestType) {\n    RequestType[RequestType[\"SICK_LEAVE\"] = 0] = \"SICK_LEAVE\";\n    RequestType[RequestType[\"CASUAL_LEAVE\"] = 1] = \"CASUAL_LEAVE\";\n    RequestType[RequestType[\"WORK_FROM_HOME\"] = 2] = \"WORK_FROM_HOME\";\n})(RequestType || (RequestType = {}));\nvar RoleRequestLabel;\n(function (RoleRequestLabel) {\n    RoleRequestLabel[\"SICK_LEAVE\"] = \"Sick Leave\";\n    RoleRequestLabel[\"CASUAL_LEAVE\"] = \"Casual Leave\";\n    RoleRequestLabel[\"ANNUAL_LEAVE\"] = \"Annual Leave\";\n    RoleRequestLabel[\"MATERNITY_LEAVE\"] = \"Maternity Leave\";\n    RoleRequestLabel[\"PATERNITY_LEAVE\"] = \"Paternity Leave\";\n    RoleRequestLabel[\"BEREAVEMENT_LEAVE\"] = \"Bereavement Leave\";\n    RoleRequestLabel[\"WORK_FROM_HOME\"] = \"Work From Home\";\n})(RoleRequestLabel || (RoleRequestLabel = {}));\n\nvar emsSupportPriority;\n(function (emsSupportPriority) {\n    emsSupportPriority[emsSupportPriority[\"High\"] = 0] = \"High\";\n    emsSupportPriority[emsSupportPriority[\"Medium\"] = 1] = \"Medium\";\n    emsSupportPriority[emsSupportPriority[\"Low\"] = 2] = \"Low\";\n})(emsSupportPriority || (emsSupportPriority = {}));\nvar emsSupportType;\n(function (emsSupportType) {\n    emsSupportType[emsSupportType[\"Request\"] = 0] = \"Request\";\n    emsSupportType[emsSupportType[\"Incident\"] = 1] = \"Incident\";\n})(emsSupportType || (emsSupportType = {}));\nvar emsSupportCategory;\n(function (emsSupportCategory) {\n    emsSupportCategory[emsSupportCategory[\"Request\"] = 0] = \"Request\";\n})(emsSupportCategory || (emsSupportCategory = {}));\n\nvar TicketPriority;\n(function (TicketPriority) {\n    TicketPriority[\"LOW_PRIORITY\"] = \"LOW PRIORITY\";\n    TicketPriority[\"MEDIUM_PRIORITY\"] = \"MEDIUM PRIORITY\";\n    TicketPriority[\"HIGH_PRIORITY\"] = \"HIGH PRIORITY\";\n})(TicketPriority || (TicketPriority = {}));\nvar TicketDepartment;\n(function (TicketDepartment) {\n    TicketDepartment[\"IT_DEPARTMENT\"] = \"IT DEPARTMENT\";\n    TicketDepartment[\"HR_DEPARTMENT\"] = \"HR DEPARTMENT\";\n    TicketDepartment[\"FINANCE_DEPARTMENT\"] = \"FINANCE DEPARTMENT\";\n    TicketDepartment[\"DEV_DEPARTMENT\"] = \"DEV DEPARTMENT\";\n})(TicketDepartment || (TicketDepartment = {}));\nvar TicketStatus;\n(function (TicketStatus) {\n    TicketStatus[\"OPEN\"] = \"OPEN\";\n    TicketStatus[\"PICKED\"] = \"PICKED\";\n    TicketStatus[\"CLOSED\"] = \"CLOSED\";\n})(TicketStatus || (TicketStatus = {}));\nvar TicketCategory;\n(function (TicketCategory) {\n    TicketCategory[\"MANAGER\"] = \"MANAGER\";\n    TicketCategory[\"HR_MANAGEMENT\"] = \"HR_MANAGEMENT\";\n    TicketCategory[\"IT_SUPPORT\"] = \"IT_SUPPORT\";\n})(TicketCategory || (TicketCategory = {}));\nvar TicketUrgency;\n(function (TicketUrgency) {\n    TicketUrgency[TicketUrgency[\"LOW\"] = 4] = \"LOW\";\n    TicketUrgency[TicketUrgency[\"MEDIUM\"] = 3] = \"MEDIUM\";\n    TicketUrgency[TicketUrgency[\"HIGH\"] = 2] = \"HIGH\";\n    TicketUrgency[TicketUrgency[\"URGENT\"] = 1] = \"URGENT\";\n})(TicketUrgency || (TicketUrgency = {}));\nvar TicketImpactCategory;\n(function (TicketImpactCategory) {\n    TicketImpactCategory[\"INDIVIDUAL\"] = \"INDIVIDUAL\";\n    TicketImpactCategory[\"TEAM\"] = \"TEAM\";\n    TicketImpactCategory[\"DEPARTMENT\"] = \"DEPARTMENT\";\n})(TicketImpactCategory || (TicketImpactCategory = {}));\nvar DocumentType;\n(function (DocumentType) {\n    DocumentType[\"PDF\"] = \"PDF\";\n    DocumentType[\"IMAGE\"] = \"IMAGE\";\n    DocumentType[\"WORD\"] = \"WORD\";\n    DocumentType[\"EXCEL\"] = \"EXCEL\";\n    DocumentType[\"OTHER\"] = \"OTHER\";\n})(DocumentType || (DocumentType = {}));\n\nvar EmsAttendanceType;\n(function (EmsAttendanceType) {\n    EmsAttendanceType[EmsAttendanceType[\"ONLINE\"] = 0] = \"ONLINE\";\n    EmsAttendanceType[EmsAttendanceType[\"OFFLINE\"] = 1] = \"OFFLINE\";\n})(EmsAttendanceType || (EmsAttendanceType = {}));\nvar EmsUserLeaveStatus;\n(function (EmsUserLeaveStatus) {\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"PENDING\"] = 1] = \"PENDING\";\n    EmsUserLeaveStatus[EmsUserLeaveStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n})(EmsUserLeaveStatus || (EmsUserLeaveStatus = {}));\nvar EmsAttendanceStatus;\n(function (EmsAttendanceStatus) {\n    EmsAttendanceStatus[EmsAttendanceStatus[\"FULL_DAY\"] = 0] = \"FULL_DAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"HALF_DAY\"] = 1] = \"HALF_DAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"ERROR\"] = 2] = \"ERROR\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"ABSENT\"] = 3] = \"ABSENT\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"WEEK_OFF\"] = 4] = \"WEEK_OFF\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"LEAVE\"] = 5] = \"LEAVE\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"HOLIDAY\"] = 6] = \"HOLIDAY\";\n    EmsAttendanceStatus[EmsAttendanceStatus[\"LESS_THAN_HALF_DAY\"] = 7] = \"LESS_THAN_HALF_DAY\";\n})(EmsAttendanceStatus || (EmsAttendanceStatus = {}));\n;\nvar EmsUserRequestStatus;\n(function (EmsUserRequestStatus) {\n    EmsUserRequestStatus[EmsUserRequestStatus[\"APPROVED\"] = 0] = \"APPROVED\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"PENDING\"] = 1] = \"PENDING\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"REJECTED\"] = 2] = \"REJECTED\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"UNPAID\"] = 3] = \"UNPAID\";\n    EmsUserRequestStatus[EmsUserRequestStatus[\"CANCEL\"] = 4] = \"CANCEL\";\n})(EmsUserRequestStatus || (EmsUserRequestStatus = {}));\n;\n\n// export interface IUserListDTO extends IEmployeeCodeInfoDTO {\n//   sEmail: string;\n//   sCode: string;\n//   aPhoneNumber: number;\n//   sName: string;\n//   sProfileUrl?: string;\n//   tOrganization: IOrganization;\n//   dJoiningDate: Date;\n//   tRole: IRole;\n//   bIsActive: boolean;\n// }\n\nconst Ems_CONSTANT_DATA = {\n    ADMIN: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: 'fdfd dfdf',\n        },\n    },\n    USER: {\n        QCM: {\n            QUIZ_SUCCESS_MSG: 'fdfd dfdf',\n        },\n    },\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AdvanceStatus, AnnouncementType, AttendanceStatus, AttendanceType, CalendarEventType, DeductType, DocumentType, EmsAttendanceStatus, EmsAttendanceType, EmsUserLeaveStatus, EmsUserRequestStatus, Ems_CONSTANT_DATA, FileType, GovtSub, IncentiveType, LeaveType, OrganizationSettingLabels, OrganizationSettingType, ProcessType, RequestStatus, RequestType, RoleRequestLabel, RoleSettingLabels, SalaryStructureCategory, SkillCategory, TicketCategory, TicketDepartment, TicketImpactCategory, TicketPriority, TicketStatus, TicketUrgency, UpdatedSalaryStructureCategory, UserAccess, emsSupportCategory, emsSupportPriority, emsSupportType };\n//# sourceMappingURL=azaadi-packages.mjs.map\n", "map": null, "preliminaryFileName": "azaadi-packages.mjs", "sourcemapFileName": "azaadi-packages.mjs.map"}, {"fileName": "azaadi-packages.mjs.map", "names": [], "needsCodeReference": false, "originalFileName": null, "originalFileNames": [], "source": "{\"version\":3,\"file\":\"azaadi-packages.mjs\",\"sources\":[\"../../../projects/azaadi-packages/src/Admin/lib/enums/attendance.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/enums/managements.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/enums/request.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/enums/support.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/enums/ticket.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/enums/users.enum.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/interface/DTO/userInfo.dto.interface.ts\",\"../../../projects/azaadi-packages/src/Admin/lib/utils/constants.ts\",\"../../../projects/azaadi-packages/src/azaadi-packages.ts\"],\"sourcesContent\":[\"export enum AttendanceStatus {\\r\\n  PRESENT = 'Present',\\r\\n  Leave = 'Leave',\\r\\n  PUBLIC_HOLIDAY = 'Public Holiday',\\r\\n  ORGANIZATION_WEEKEND = 'Organization Weekend',\\r\\n  ERROR = 'Error',\\r\\n  TODAYS_DATE = \\\"Today's Date\\\",\\r\\n  LHD = 'Less Than Half Day',\\r\\n  HD = 'Half Day',\\r\\n}\\r\\n\\r\\nexport enum AttendanceType {\\r\\n  ONLINE = 0,\\r\\n  OFFLINE = 1,\\r\\n}\\r\\n\",\"export enum UserAccess {\\r\\n  READ,\\r\\n  WRITE,\\r\\n  ALL,\\r\\n}\\r\\nexport enum FileType {\\r\\n  pdf,\\r\\n  jpg,\\r\\n  jpeg,\\r\\n  doc,\\r\\n  excel,\\r\\n  xml,\\r\\n}\\r\\n\\r\\nexport enum ProcessType {\\r\\n  EXAM,\\r\\n  INTERVIEW,\\r\\n  DOCUMENT,\\r\\n  BACKGROUND,\\r\\n}\\r\\n\\r\\nexport declare enum InterviewMode {\\r\\n  ONLINE = 0,\\r\\n  OFFLINE = 1,\\r\\n}\\r\\n\\r\\nexport enum LeaveType {\\r\\n  EXAM,\\r\\n  PERSONAL,\\r\\n  SICK,\\r\\n  UNPAID,\\r\\n  OTHERS,\\r\\n}\\r\\n\\r\\nexport enum CalendarEventType {\\r\\n  HOLIDAY,\\r\\n  WEEKOFF,\\r\\n  EVENT,\\r\\n}\\r\\n\\r\\nexport enum OrganizationSettingType {\\r\\n  SICK_LEAVE,\\r\\n  PERSONAL_LEAVE,\\r\\n  EXAM_LEAVE,\\r\\n  OTHER_LEAVE,\\r\\n  UNPAID_LEAVE,\\r\\n  WORKING_HOUR,\\r\\n  DESCRIPTION,\\r\\n  BRAND_NAME,\\r\\n  LOGO,\\r\\n  PHONE,\\r\\n  ADDRESS,\\r\\n  TOTAL_EMPLOYEE,\\r\\n  HOLIDAY,\\r\\n  WEEKOFF,\\r\\n  SIGN_IN,\\r\\n  SIGN_OUT,\\r\\n  SALARY_CIRCLE_START_DATE,\\r\\n  SALARY_CIRCLE_END_DATE,\\r\\n}\\r\\n\\r\\nexport enum OrganizationSettingLabels {\\r\\n  Logo = 'Logo',\\r\\n  TotalAllowEmployee = 'Total Allow Employee',\\r\\n  SalaryDay = 'Salary Day',\\r\\n  Phone = 'Phone No.',\\r\\n  OfficialEmail = 'Official Email',\\r\\n  Website = 'Website',\\r\\n  Address = 'Address',\\r\\n  Description = 'Description',\\r\\n  IncentiveDay = 'Incentive Day',\\r\\n  TDSApplicable = 'TDS Deduction Not Applicable',\\r\\n}\\r\\n\\r\\nexport enum RoleSettingLabels {\\r\\n  LateLogin = 'Late Login (min)',\\r\\n  EarlyLogout = 'Early Logout (min)',\\r\\n  PunchIn = 'Punch In Time',\\r\\n  PunchOut = 'Punch Out Time',\\r\\n  WeekOff = 'Week Off',\\r\\n  Holidays = 'Calendar',\\r\\n  isAdmin = 'Admin?',\\r\\n}\\r\\n\\r\\nexport enum SalaryStructureCategory {\\r\\n  NONPF = 0,\\r\\n  PF1 = 1,\\r\\n  PF2 = 2,\\r\\n}\\r\\nexport enum UpdatedSalaryStructureCategory {\\r\\n  NONPF = 0,\\r\\n  PF = 1,\\r\\n  // PFESI = 2,\\r\\n}\\r\\nexport enum GovtSub {\\r\\n  ABRY = 0,\\r\\n  NotApplicable = 1,\\r\\n}\\r\\n\\r\\nexport enum SkillCategory {\\r\\n  Unskilled = 0,\\r\\n  SemiSkilled = 1,\\r\\n  Skilled = 2,\\r\\n  HighlySkilled = 3,\\r\\n  NotFollowed = 4,\\r\\n}\\r\\nexport enum AnnouncementType {\\r\\n  Announcement = 0,\\r\\n  Birthday = 1,\\r\\n  WorkAnniversary = 2,\\r\\n  CalendarEvent = 3,\\r\\n}\\r\\n\\r\\nexport enum IncentiveType {\\r\\n  Monthly = 'Monthly',\\r\\n  Quarterly = 'Quarterly',\\r\\n  Yearly = 'Yearly',\\r\\n  HalfYearly = 'Half yearly',\\r\\n}\\r\\n\\r\\nexport enum DeductType {\\r\\n  Monthly = 'Monthly',\\r\\n  Quarterly = 'Quarterly',\\r\\n  HalfYearly = 'Half Yearly',\\r\\n  Yearly = 'Yearly',\\r\\n}\\r\\nexport enum AdvanceStatus {\\r\\n  Request = 'Request',\\r\\n  Approved = 'Approved',\\r\\n  Pending = 'Pending',\\r\\n  HrApproved = 'Approved By Hr',\\r\\n  Rejected = 'Rejected',\\r\\n  Canceled = 'Canceled',\\r\\n}\\r\\n\",\"export enum RequestStatus {\\r\\n  APPROVED = 0,\\r\\n  PENDING = 1,\\r\\n  REJECTED = 2,\\r\\n  UNPAID = 3,\\r\\n  CANCELLED = 4,\\r\\n}\\r\\n\\r\\nexport enum RequestType {\\r\\n  SICK_LEAVE = 0,\\r\\n  CASUAL_LEAVE = 1,\\r\\n  WORK_FROM_HOME = 2,\\r\\n}\\r\\nexport enum RoleRequestLabel {\\r\\n  SICK_LEAVE = 'Sick Leave',\\r\\n  CASUAL_LEAVE = 'Casual Leave',\\r\\n  ANNUAL_LEAVE = 'Annual Leave',\\r\\n  MATERNITY_LEAVE = 'Maternity Leave',\\r\\n  PATERNITY_LEAVE = 'Paternity Leave',\\r\\n  BEREAVEMENT_LEAVE = 'Bereavement Leave',\\r\\n  WORK_FROM_HOME = 'Work From Home',\\r\\n}\\r\\n\",\"export enum emsSupportPriority {\\r\\n    High,\\r\\n    Medium,\\r\\n    Low\\r\\n}\\r\\nexport enum emsSupportType {\\r\\n    Request,\\r\\n    Incident\\r\\n}\\r\\nexport enum emsSupportCategory {\\r\\n    Request\\r\\n}\",\"export enum TicketPriority {\\r\\n  LOW_PRIORITY = 'LOW PRIORITY',\\r\\n  MEDIUM_PRIORITY = 'MEDIUM PRIORITY',\\r\\n  HIGH_PRIORITY = 'HIGH PRIORITY',\\r\\n}\\r\\n\\r\\nexport enum TicketDepartment {\\r\\n  IT_DEPARTMENT = 'IT DEPARTMENT',\\r\\n  HR_DEPARTMENT = 'HR DEPARTMENT',\\r\\n  FINANCE_DEPARTMENT = 'FINANCE DEPARTMENT',\\r\\n  DEV_DEPARTMENT = 'DEV DEPARTMENT',\\r\\n}\\r\\nexport enum TicketStatus {\\r\\n  OPEN = 'OPEN',\\r\\n  PICKED = 'PICKED',\\r\\n  CLOSED = 'CLOSED',\\r\\n}\\r\\nexport enum TicketCategory {\\r\\n  MANAGER = 'MANAGER',\\r\\n  HR_MANAGEMENT = 'HR_MANAGEMENT',\\r\\n  IT_SUPPORT = 'IT_SUPPORT',\\r\\n}\\r\\nexport enum TicketUrgency {\\r\\n  LOW = 4,\\r\\n  MEDIUM = 3,\\r\\n  HIGH = 2,\\r\\n  URGENT = 1,\\r\\n}\\r\\nexport enum TicketImpactCategory {\\r\\n  INDIVIDUAL = 'INDIVIDUAL',\\r\\n  TEAM = 'TEAM',\\r\\n  DEPARTMENT = 'DEPARTMENT',\\r\\n}\\r\\nexport enum DocumentType {\\r\\n  PDF = 'PDF',\\r\\n  IMAGE = 'IMAGE',\\r\\n  WORD = 'WORD',\\r\\n  EXCEL = 'EXCEL',\\r\\n  OTHER = 'OTHER',\\r\\n}\\r\\n\",\"export enum EmsAttendanceType {\\r\\n    ONLINE,\\r\\n    OFFLINE,\\r\\n}\\r\\n\\r\\nexport enum EmsUserLeaveStatus {\\r\\n    APPROVED,\\r\\n    PENDING,\\r\\n    REJECTED,\\r\\n}\\r\\nexport enum EmsAttendanceStatus {\\r\\n    FULL_DAY,\\r\\n    HALF_DAY,\\r\\n    ERROR,\\r\\n    ABSENT,\\r\\n    WEEK_OFF,\\r\\n    LEAVE,\\r\\n    HOLIDAY,\\r\\n    LESS_THAN_HALF_DAY\\r\\n};\\r\\nexport enum EmsUserRequestStatus {\\r\\n    APPROVED,\\r\\n    PENDING,\\r\\n    REJECTED,\\r\\n    UNPAID,\\r\\n    CANCEL\\r\\n};\",\"import { IUserInfo } from '../User';\\r\\n\\r\\n// Reuse from IUserInfo, omitting the fields that differ in DTO\\r\\nexport interface IUserInfoDTO\\r\\n  extends Omit<\\r\\n    IUserInfo,\\r\\n    'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'\\r\\n  > {\\r\\n  /** Role of the user (as string ID) */\\r\\n  tRoleId?: string;\\r\\n\\r\\n  /** Organization ID (as string) */\\r\\n  tOrganizationId?: string;\\r\\n\\r\\n  /** Department ID (as string) */\\r\\n  tDepartmentId?: string;\\r\\n\\r\\n  /** Password of the user (optional in DTO) */\\r\\n  sPassword?: string;\\r\\n\\r\\n  /** Employee Code (renamed in DTO) */\\r\\n  empCode: string;\\r\\n}\\r\\n// export interface IUserListDTO extends IEmployeeCodeInfoDTO {\\r\\n//   sEmail: string;\\r\\n//   sCode: string;\\r\\n//   aPhoneNumber: number;\\r\\n//   sName: string;\\r\\n//   sProfileUrl?: string;\\r\\n//   tOrganization: IOrganization;\\r\\n//   dJoiningDate: Date;\\r\\n//   tRole: IRole;\\r\\n//   bIsActive: boolean;\\r\\n// }\\r\\n\",\"export const Ems_CONSTANT_DATA = {\\r\\n  ADMIN: {\\r\\n    QCM: {\\r\\n      QUIZ_SUCCESS_MSG: 'fdfd dfdf',\\r\\n    },\\r\\n  },\\r\\n  USER: {\\r\\n    QCM: {\\r\\n      QUIZ_SUCCESS_MSG: 'fdfd dfdf',\\r\\n    },\\r\\n  },\\r\\n};\\r\\n\",\"/**\\n * Generated bundle index. Do not edit.\\n */\\n\\nexport * from './public-api';\\n\"],\"names\":[],\"mappings\":\"IAAY;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACjC,IAAA,gBAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C;AAC7C,IAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,gBAAA,CAAA,aAAA,CAAA,GAAA,cAA4B;AAC5B,IAAA,gBAAA,CAAA,KAAA,CAAA,GAAA,oBAA0B;AAC1B,IAAA,gBAAA,CAAA,IAAA,CAAA,GAAA,UAAe;AACjB,CAAC,EATW,gBAAgB,KAAhB,gBAAgB,GAS3B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACb,CAAC,EAHW,cAAc,KAAd,cAAc,GAGzB,EAAA,CAAA,CAAA;;ICdW;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,UAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,UAAA,CAAA,UAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,UAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAJW,UAAU,KAAV,UAAU,GAIrB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACL,CAAC,EAPW,QAAQ,KAAR,QAAQ,GAOnB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,WAAA,CAAA,WAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAS;AACT,IAAA,WAAA,CAAA,WAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACZ,CAAC,EALW,WAAW,KAAX,WAAW,GAKtB,EAAA,CAAA,CAAA;IAOW;AAAZ,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,SAAA,CAAA,SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,SAAA,CAAA,SAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,SAAA,CAAA,SAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACR,CAAC,EANW,SAAS,KAAT,SAAS,GAMpB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,iBAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACP,CAAC,EAJW,iBAAiB,KAAjB,iBAAiB,GAI5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,uBAAuB,EAAA;AACjC,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc;AACd,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,uBAAA,CAAA,uBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;AACZ,IAAA,uBAAA,CAAA,uBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY;AACZ,IAAA,uBAAA,CAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAW;AACX,IAAA,uBAAA,CAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,uBAAA,CAAA,uBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,uBAAA,CAAA,uBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,gBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,gBAAc;AACd,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,GAAA,SAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,UAAA,CAAA,GAAA,EAAA,CAAA,GAAA,UAAQ;AACR,IAAA,uBAAA,CAAA,uBAAA,CAAA,0BAAA,CAAA,GAAA,EAAA,CAAA,GAAA,0BAAwB;AACxB,IAAA,uBAAA,CAAA,uBAAA,CAAA,wBAAA,CAAA,GAAA,EAAA,CAAA,GAAA,wBAAsB;AACxB,CAAC,EAnBW,uBAAuB,KAAvB,uBAAuB,GAmBlC,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,yBAAyB,EAAA;AACnC,IAAA,yBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,yBAAA,CAAA,oBAAA,CAAA,GAAA,sBAA2C;AAC3C,IAAA,yBAAA,CAAA,WAAA,CAAA,GAAA,YAAwB;AACxB,IAAA,yBAAA,CAAA,OAAA,CAAA,GAAA,WAAmB;AACnB,IAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,gBAAgC;AAChC,IAAA,yBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,yBAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,yBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B;AAC3B,IAAA,yBAAA,CAAA,cAAA,CAAA,GAAA,eAA8B;AAC9B,IAAA,yBAAA,CAAA,eAAA,CAAA,GAAA,8BAA8C;AAChD,CAAC,EAXW,yBAAyB,KAAzB,yBAAyB,GAWpC,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AAC3B,IAAA,iBAAA,CAAA,WAAA,CAAA,GAAA,kBAA8B;AAC9B,IAAA,iBAAA,CAAA,aAAA,CAAA,GAAA,oBAAkC;AAClC,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,eAAyB;AACzB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,gBAA2B;AAC3B,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,UAAoB;AACpB,IAAA,iBAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,QAAkB;AACpB,CAAC,EARW,iBAAiB,KAAjB,iBAAiB,GAQ5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,uBAAuB,EAAA;AACjC,IAAA,uBAAA,CAAA,uBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT,IAAA,uBAAA,CAAA,uBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,uBAAA,CAAA,uBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACT,CAAC,EAJW,uBAAuB,KAAvB,uBAAuB,GAIlC,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,8BAA8B,EAAA;AACxC,IAAA,8BAAA,CAAA,8BAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT,IAAA,8BAAA,CAAA,8BAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,GAAA,IAAM;;AAER,CAAC,EAJW,8BAA8B,KAA9B,8BAA8B,GAIzC,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,OAAO,EAAA;AACjB,IAAA,OAAA,CAAA,OAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,OAAA,CAAA,OAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACnB,CAAC,EAHW,OAAO,KAAP,OAAO,GAGlB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACb,IAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AACf,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX,IAAA,aAAA,CAAA,aAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACjB,IAAA,aAAA,CAAA,aAAA,CAAA,aAAA,CAAA,GAAA,CAAA,CAAA,GAAA,aAAe;AACjB,CAAC,EANW,aAAa,KAAb,aAAa,GAMxB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB,IAAA,gBAAA,CAAA,gBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,gBAAA,CAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,iBAAmB;AACnB,IAAA,gBAAA,CAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,GAAA,eAAiB;AACnB,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,GAK3B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,aAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,aAA0B;AAC5B,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,UAAU,EAAA;AACpB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,UAAA,CAAA,WAAA,CAAA,GAAA,WAAuB;AACvB,IAAA,UAAA,CAAA,YAAA,CAAA,GAAA,aAA0B;AAC1B,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EALW,UAAU,KAAV,UAAU,GAKrB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,aAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,aAAA,CAAA,YAAA,CAAA,GAAA,gBAA6B;AAC7B,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACrB,IAAA,aAAA,CAAA,UAAA,CAAA,GAAA,UAAqB;AACvB,CAAC,EAPW,aAAa,KAAb,aAAa,GAOxB,EAAA,CAAA,CAAA;;ICrIW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,aAAA,CAAA,aAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW;AACX,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACZ,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,WAAA,CAAA,GAAA,CAAA,CAAA,GAAA,WAAa;AACf,CAAC,EANW,aAAa,KAAb,aAAa,GAMxB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,WAAW,EAAA;AACrB,IAAA,WAAA,CAAA,WAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAc;AACd,IAAA,WAAA,CAAA,WAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAgB;AAChB,IAAA,WAAA,CAAA,WAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAkB;AACpB,CAAC,EAJW,WAAW,KAAX,WAAW,GAItB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AACzB,IAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,gBAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,gBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,gBAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC;AACvC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACnC,CAAC,EARW,gBAAgB,KAAhB,gBAAgB,GAQ3B,EAAA,CAAA,CAAA;;ICrBW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,kBAAA,CAAA,kBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,kBAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACP,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,GAI7B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACtB,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACZ,CAAC,EAHW,cAAc,KAAd,cAAc,GAGzB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACX,CAAC,EAFW,kBAAkB,KAAlB,kBAAkB,GAE7B,EAAA,CAAA,CAAA;;ICXW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,GAAA,cAA6B;AAC7B,IAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC;AACnC,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AACjC,CAAC,EAJW,cAAc,KAAd,cAAc,GAIzB,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,gBAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,gBAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC;AACzC,IAAA,gBAAA,CAAA,gBAAA,CAAA,GAAA,gBAAiC;AACnC,CAAC,EALW,gBAAgB,KAAhB,gBAAgB,GAK3B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACjB,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB;AACnB,CAAC,EAJW,YAAY,KAAZ,YAAY,GAIvB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,SAAA,CAAA,GAAA,SAAmB;AACnB,IAAA,cAAA,CAAA,eAAA,CAAA,GAAA,eAA+B;AAC/B,IAAA,cAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AAC3B,CAAC,EAJW,cAAc,KAAd,cAAc,GAIzB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,aAAa,EAAA;AACvB,IAAA,aAAA,CAAA,aAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACP,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAQ;AACR,IAAA,aAAA,CAAA,aAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACZ,CAAC,EALW,aAAa,KAAb,aAAa,GAKxB,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,oBAAoB,EAAA;AAC9B,IAAA,oBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AACzB,IAAA,oBAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,oBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB;AAC3B,CAAC,EAJW,oBAAoB,KAApB,oBAAoB,GAI/B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,KAAA,CAAA,GAAA,KAAW;AACX,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa;AACb,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACf,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe;AACjB,CAAC,EANW,YAAY,KAAZ,YAAY,GAMvB,EAAA,CAAA,CAAA;;ICvCW;AAAZ,CAAA,UAAY,iBAAiB,EAAA;AACzB,IAAA,iBAAA,CAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,iBAAA,CAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACX,CAAC,EAHW,iBAAiB,KAAjB,iBAAiB,GAG5B,EAAA,CAAA,CAAA;IAEW;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC1B,IAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,kBAAA,CAAA,kBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACZ,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,GAI7B,EAAA,CAAA,CAAA;IACW;AAAZ,CAAA,UAAY,mBAAmB,EAAA;AAC3B,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,mBAAA,CAAA,mBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,mBAAA,CAAA,mBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,mBAAA,CAAA,mBAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,mBAAA,CAAA,mBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,mBAAA,CAAA,mBAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,oBAAkB;AACtB,CAAC,EATW,mBAAmB,KAAnB,mBAAmB,GAS9B,EAAA,CAAA,CAAA;AAAA;IACW;AAAZ,CAAA,UAAY,oBAAoB,EAAA;AAC5B,IAAA,oBAAA,CAAA,oBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,oBAAA,CAAA,oBAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,oBAAA,CAAA,oBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACR,IAAA,oBAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACN,IAAA,oBAAA,CAAA,oBAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACV,CAAC,EANW,oBAAoB,KAApB,oBAAoB,GAM/B,EAAA,CAAA,CAAA;AAAA;;ACHD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACjCa,MAAA,iBAAiB,GAAG;AAC/B,IAAA,KAAK,EAAE;AACL,QAAA,GAAG,EAAE;AACH,YAAA,gBAAgB,EAAE,WAAW;AAC9B,SAAA;AACF,KAAA;AACD,IAAA,IAAI,EAAE;AACJ,QAAA,GAAG,EAAE;AACH,YAAA,gBAAgB,EAAE,WAAW;AAC9B,SAAA;AACF,KAAA;;;ACVH;;AAEG;;;;\"}", "type": "asset"}]}