export enum UserAccess {
  READ,
  WRITE,
  ALL,
}
export enum FileType {
  pdf,
  jpg,
  jpeg,
  doc,
  excel,
  xml,
}

export enum ProcessType {
  EXAM,
  INTERVIEW,
  DOCUMENT,
  BACKGROUND,
}

export declare enum InterviewMode {
  ONLINE = 0,
  OFFLINE = 1,
}

export enum LeaveType {
  EXAM,
  PERSONAL,
  SICK,
  UNPAID,
  OTHERS,
}

export enum CalendarEventType {
  HOLIDAY,
  WEEKOFF,
  EVENT,
}

export enum OrganizationSettingType {
  SICK_LEAVE,
  PERSONAL_LEAVE,
  EXAM_LEAVE,
  OTHER_LEAVE,
  UNPAID_LEAVE,
  WORKING_HOUR,
  DESCRIPTION,
  BRAND_NAME,
  LOGO,
  PHONE,
  ADDRESS,
  TOTAL_EMPLOYEE,
  HOLIDAY,
  WEEKOFF,
  SIGN_IN,
  SIGN_OUT,
  SALARY_CIRCLE_START_DATE,
  SALARY_CIRCLE_END_DATE,
}

export enum OrganizationSettingLabels {
  Logo = 'Logo',
  TotalAllowEmployee = 'Total Allow Employee',
  SalaryDay = 'Salary Day',
  Phone = 'Phone No.',
  OfficialEmail = 'Official Email',
  Website = 'Website',
  Address = 'Address',
  Description = 'Description',
  IncentiveDay = 'Incentive Day',
  TDSApplicable = 'TDS Deduction Not Applicable',
}

export enum RoleSettingLabels {
  LateLogin = 'Late Login (min)',
  EarlyLogout = 'Early Logout (min)',
  PunchIn = 'Punch In Time',
  PunchOut = 'Punch Out Time',
  WeekOff = 'Week Off',
  Holidays = 'Calendar',
  isAdmin = 'Admin?',
}

export enum SalaryStructureCategory {
  NONPF = 0,
  PF1 = 1,
  PF2 = 2,
}
export enum UpdatedSalaryStructureCategory {
  NONPF = 0,
  PF = 1,
  // PFESI = 2,
}
export enum GovtSub {
  ABRY = 0,
  NotApplicable = 1,
}

export enum SkillCategory {
  Unskilled = 0,
  SemiSkilled = 1,
  Skilled = 2,
  HighlySkilled = 3,
  NotFollowed = 4,
}
export enum AnnouncementType {
  Announcement = 0,
  Birthday = 1,
  WorkAnniversary = 2,
  CalendarEvent = 3,
}

export enum IncentiveType {
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  Yearly = 'Yearly',
  HalfYearly = 'Half yearly',
}

export enum DeductType {
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  HalfYearly = 'Half Yearly',
  Yearly = 'Yearly',
}
export enum AdvanceStatus {
  Request = 'Request',
  Approved = 'Approved',
  Pending = 'Pending',
  HrApproved = 'Approved By Hr',
  Rejected = 'Rejected',
  Canceled = 'Canceled',
}
