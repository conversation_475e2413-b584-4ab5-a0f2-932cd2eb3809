import { IConcept } from '../Common';
import { IUser, IUserInfoModel } from '../User';
import { ITicketModel } from './ticket.interface';

export interface IMessage {
  sender: IUser;
  content: string;
  date: Date;
  isRead?: boolean;
  receiver?: IUser;
}
export interface INotification {
  subject: string;
  message: string;
}
export interface IChatModel extends IConcept {
  tTicketId: string | ITicketModel;
  tSenderId: string | IUserInfoModel;
  sContent: string;
}
