import { RoleRequestLabel } from '../../enums';
import { Identifiable } from '../Common';

export interface IRequest {
  dDate: Date[];
  eStatus: number;
  eType: string;
  tSender: string;
  sReason: string;
  sMessage: string;
  tApproverBy: string;
}
export interface ILeaveBalance extends Identifiable {
  sType: RoleRequestLabel;
  aTotalBalance: 5;
  aAvailableBalance: 5;
}
export interface ILeaveBalanceRequest {
  organizationId: string;
  currentYear: number;
}
