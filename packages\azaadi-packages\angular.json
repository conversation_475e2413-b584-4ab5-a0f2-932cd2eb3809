{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"azaadi-packages-application": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/application/azaadi-packages", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": [], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "azaadi-packages-application:build:production"}, "development": {"buildTarget": "azaadi-packages-application:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "azaadi-packages-application:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}, "azaadi-packages": {"projectType": "library", "root": "projects/azaadi-packages", "sourceRoot": "projects/azaadi-packages/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/azaadi-packages/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/azaadi-packages/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/azaadi-packages/tsconfig.lib.json"}}, "defaultConfiguration": "production"}}}}, "cli": {"analytics": "c0881d40-eb34-44b5-a76a-23bba7a52330"}}