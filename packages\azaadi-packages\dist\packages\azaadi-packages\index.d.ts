/// <reference types="mongoose/types/aggregate" />
/// <reference types="mongoose/types/callback" />
/// <reference types="mongoose/types/collection" />
/// <reference types="mongoose/types/connection" />
/// <reference types="mongoose/types/cursor" />
/// <reference types="mongoose/types/document" />
/// <reference types="mongoose/types/error" />
/// <reference types="mongoose/types/expressions" />
/// <reference types="mongoose/types/helpers" />
/// <reference types="mongoose/types/middlewares" />
/// <reference types="mongoose/types/indexes" />
/// <reference types="mongoose/types/models" />
/// <reference types="mongoose/types/mongooseoptions" />
/// <reference types="mongoose/types/pipelinestage" />
/// <reference types="mongoose/types/populate" />
/// <reference types="mongoose/types/query" />
/// <reference types="mongoose/types/schemaoptions" />
/// <reference types="mongoose/types/session" />
/// <reference types="mongoose/types/types" />
/// <reference types="mongoose/types/utility" />
/// <reference types="mongoose/types/validation" />
/// <reference types="mongoose/types/virtuals" />
/// <reference types="mongoose/types/schematypes" />
/// <reference types="mongoose/types/inferschematype" />
/// <reference types="mongoose/types/inferrawdoctype" />
import { Types, Document, SchemaTimestampsConfig, Schema } from 'mongoose';

declare enum AttendanceStatus {
    PRESENT = "Present",
    Leave = "Leave",
    PUBLIC_HOLIDAY = "Public Holiday",
    ORGANIZATION_WEEKEND = "Organization Weekend",
    ERROR = "Error",
    TODAYS_DATE = "Today's Date",
    LHD = "Less Than Half Day",
    HD = "Half Day"
}
declare enum AttendanceType {
    ONLINE = 0,
    OFFLINE = 1
}

declare enum UserAccess {
    READ = 0,
    WRITE = 1,
    ALL = 2
}
declare enum FileType {
    pdf = 0,
    jpg = 1,
    jpeg = 2,
    doc = 3,
    excel = 4,
    xml = 5
}
declare enum ProcessType {
    EXAM = 0,
    INTERVIEW = 1,
    DOCUMENT = 2,
    BACKGROUND = 3
}
declare enum InterviewMode {
    ONLINE = 0,
    OFFLINE = 1
}
declare enum LeaveType {
    EXAM = 0,
    PERSONAL = 1,
    SICK = 2,
    UNPAID = 3,
    OTHERS = 4
}
declare enum CalendarEventType {
    HOLIDAY = 0,
    WEEKOFF = 1,
    EVENT = 2
}
declare enum OrganizationSettingType {
    SICK_LEAVE = 0,
    PERSONAL_LEAVE = 1,
    EXAM_LEAVE = 2,
    OTHER_LEAVE = 3,
    UNPAID_LEAVE = 4,
    WORKING_HOUR = 5,
    DESCRIPTION = 6,
    BRAND_NAME = 7,
    LOGO = 8,
    PHONE = 9,
    ADDRESS = 10,
    TOTAL_EMPLOYEE = 11,
    HOLIDAY = 12,
    WEEKOFF = 13,
    SIGN_IN = 14,
    SIGN_OUT = 15,
    SALARY_CIRCLE_START_DATE = 16,
    SALARY_CIRCLE_END_DATE = 17
}
declare enum OrganizationSettingLabels {
    Logo = "Logo",
    TotalAllowEmployee = "Total Allow Employee",
    SalaryDay = "Salary Day",
    Phone = "Phone No.",
    OfficialEmail = "Official Email",
    Website = "Website",
    Address = "Address",
    Description = "Description",
    IncentiveDay = "Incentive Day",
    TDSApplicable = "TDS Deduction Not Applicable"
}
declare enum RoleSettingLabels {
    LateLogin = "Late Login (min)",
    EarlyLogout = "Early Logout (min)",
    PunchIn = "Punch In Time",
    PunchOut = "Punch Out Time",
    WeekOff = "Week Off",
    Holidays = "Calendar",
    isAdmin = "Admin?"
}
declare enum SalaryStructureCategory {
    NONPF = 0,
    PF1 = 1,
    PF2 = 2
}
declare enum UpdatedSalaryStructureCategory {
    NONPF = 0,
    PF = 1
}
declare enum GovtSub {
    ABRY = 0,
    NotApplicable = 1
}
declare enum SkillCategory {
    Unskilled = 0,
    SemiSkilled = 1,
    Skilled = 2,
    HighlySkilled = 3,
    NotFollowed = 4
}
declare enum AnnouncementType {
    Announcement = 0,
    Birthday = 1,
    WorkAnniversary = 2,
    CalendarEvent = 3
}
declare enum IncentiveType {
    Monthly = "Monthly",
    Quarterly = "Quarterly",
    Yearly = "Yearly",
    HalfYearly = "Half yearly"
}
declare enum DeductType {
    Monthly = "Monthly",
    Quarterly = "Quarterly",
    HalfYearly = "Half Yearly",
    Yearly = "Yearly"
}
declare enum AdvanceStatus {
    Request = "Request",
    Approved = "Approved",
    Pending = "Pending",
    HrApproved = "Approved By Hr",
    Rejected = "Rejected",
    Canceled = "Canceled"
}

declare enum RequestStatus {
    APPROVED = 0,
    PENDING = 1,
    REJECTED = 2,
    UNPAID = 3,
    CANCELLED = 4
}
declare enum RequestType {
    SICK_LEAVE = 0,
    CASUAL_LEAVE = 1,
    WORK_FROM_HOME = 2
}
declare enum RoleRequestLabel {
    SICK_LEAVE = "Sick Leave",
    CASUAL_LEAVE = "Casual Leave",
    ANNUAL_LEAVE = "Annual Leave",
    MATERNITY_LEAVE = "Maternity Leave",
    PATERNITY_LEAVE = "Paternity Leave",
    BEREAVEMENT_LEAVE = "Bereavement Leave",
    WORK_FROM_HOME = "Work From Home"
}

declare enum emsSupportPriority {
    High = 0,
    Medium = 1,
    Low = 2
}
declare enum emsSupportType {
    Request = 0,
    Incident = 1
}
declare enum emsSupportCategory {
    Request = 0
}

declare enum TicketPriority {
    LOW_PRIORITY = "LOW PRIORITY",
    MEDIUM_PRIORITY = "MEDIUM PRIORITY",
    HIGH_PRIORITY = "HIGH PRIORITY"
}
declare enum TicketDepartment {
    IT_DEPARTMENT = "IT DEPARTMENT",
    HR_DEPARTMENT = "HR DEPARTMENT",
    FINANCE_DEPARTMENT = "FINANCE DEPARTMENT",
    DEV_DEPARTMENT = "DEV DEPARTMENT"
}
declare enum TicketStatus {
    OPEN = "OPEN",
    PICKED = "PICKED",
    CLOSED = "CLOSED"
}
declare enum TicketCategory {
    MANAGER = "MANAGER",
    HR_MANAGEMENT = "HR_MANAGEMENT",
    IT_SUPPORT = "IT_SUPPORT"
}
declare enum TicketUrgency {
    LOW = 4,
    MEDIUM = 3,
    HIGH = 2,
    URGENT = 1
}
declare enum TicketImpactCategory {
    INDIVIDUAL = "INDIVIDUAL",
    TEAM = "TEAM",
    DEPARTMENT = "DEPARTMENT"
}
declare enum DocumentType {
    PDF = "PDF",
    IMAGE = "IMAGE",
    WORD = "WORD",
    EXCEL = "EXCEL",
    OTHER = "OTHER"
}

declare enum EmsAttendanceType {
    ONLINE = 0,
    OFFLINE = 1
}
declare enum EmsUserLeaveStatus {
    APPROVED = 0,
    PENDING = 1,
    REJECTED = 2
}
declare enum EmsAttendanceStatus {
    FULL_DAY = 0,
    HALF_DAY = 1,
    ERROR = 2,
    ABSENT = 3,
    WEEK_OFF = 4,
    LEAVE = 5,
    HOLIDAY = 6,
    LESS_THAN_HALF_DAY = 7
}
declare enum EmsUserRequestStatus {
    APPROVED = 0,
    PENDING = 1,
    REJECTED = 2,
    UNPAID = 3,
    CANCEL = 4
}

interface Identifiable {
    _id: string | Types.ObjectId;
}
interface ITimeStamp {
    createdAt?: Date;
    updatedAt?: Date;
}
interface INameEntity extends Identifiable {
    sName: string;
}
interface ITagEntity extends INameEntity {
    sTag?: string;
}
interface ITimeStampIdentifiable extends ITimeStamp, Identifiable {
}
interface IActiveStatus {
    bIsActive: boolean;
}
interface IStartDateEndDate {
    dStartDate: Date;
    dEndDate: Date;
}
interface IConcept extends Document, SchemaTimestampsConfig {
}

interface IAccess extends ITagEntity {
    tRole: string;
    bCanView: boolean;
    bCanWrite: boolean;
}

interface IAsset {
    id: number;
    name: string;
    imageUrl: string;
    dateAcquired: Date;
    status: string;
}

interface IAnniversary {
    user: IUser;
    anniversaryDate: Date;
}
interface IBirthday {
    user: IUser;
    birthdayDate: Date;
}

interface IOrganizationBase {
    /** Tag/identifier for the organization */
    sTag: string;
    /** Name of the organization */
    sName: string;
    /** Description of the organization */
    sDescription: string;
    /** Branch /company name */
    sBrandName: string;
    /** Logo URL or path */
    sLogo: string;
    /** Organization's email address */
    sEmail: string;
}
interface IOrganization extends IOrganizationBase, Identifiable {
}
interface IOrganizationModel extends IConcept, IOrganizationBase {
}

interface IDepartment {
    /** Name of the department */
    sName: string;
    /** Reference to the department head */
    tDepartmentHead: string | Types.ObjectId | IUserInfo;
    /** Reference to the parent organization */
    tOrganization: string | Types.ObjectId | IOrganization;
}
interface IDepartmentModel extends IConcept, IDepartment {
}

interface IDesignationInfoDTO extends ITagEntity {
}

interface IHoliday {
    title: string;
    date: Date;
    description: string;
    image: string;
}

interface IPost {
    title?: string;
    body: string;
    user: IUser;
    date: Date;
}

interface IShift extends INameEntity {
    sTimezone: string;
    sTime: string;
    sPunchInTime: string;
    sPunchOutTime: string;
    sTimeBuffer: string;
    isDefault: boolean;
    tOrganization: IOrganization;
}

interface ITodaysAttendance extends IStartDateEndDate {
    eAttendanceType: AttendanceType;
    eEndAttendanceType: AttendanceType;
    tShift: string;
}
interface IAttendanceRequest {
    organizationId: string;
    shiftId: string;
    currentDate: string;
    departmentId?: string;
}
interface ITodaysAttendanceResponse {
    dStartDate: Date[];
    dEndDate: Date[];
    eAttendanceType: AttendanceType;
    eEndAttendanceType: AttendanceType;
    tShift: IShift;
}
interface IAttendanceReport {
    report: IDayAttendance[];
}
interface IDayAttendance {
    status: AttendanceStatus;
    calendarDates: Date | null;
}

interface IRequest {
    dDate: Date[];
    eStatus: number;
    eType: string;
    tSender: string;
    sReason: string;
    sMessage: string;
    tApproverBy: string;
}
interface ILeaveBalance extends Identifiable {
    sType: RoleRequestLabel;
    aTotalBalance: 5;
    aAvailableBalance: 5;
}
interface ILeaveBalanceRequest {
    organizationId: string;
    currentYear: number;
}

interface IUserDetails extends Identifiable, IActiveStatus {
    sName: string;
    tDepartment: IDepartment;
    dJoinDate: string;
    tOrganization: IOrganization;
    tEducationInfo: any[];
    tExperienceInfo: any[];
}
interface IUserInfo {
    /** Full name of the user */
    sName: string;
    /** Email address of the user */
    sEmail: string;
    /** Hashed password of the user */
    sPassword: string;
    /** ProfilePicture of the user */
    sProfilePicture: string;
    /** Role of the user */
    tRoleId: string | Types.ObjectId | IRole;
    /** EmployeeCode of the user */
    tEmpCode: string;
    /** Status of the user */
    eStatus: string;
    /** Reference to the organization the user belongs to */
    tOrganizationId: string | Types.ObjectId;
    /** Reference to the user's role in the system */
    tDepartmentId: string | Types.ObjectId;
    /** Reference to the user's department */
    sDeviceToken?: string;
}
interface IUserInfoModel extends IConcept, IUserInfo {
}

interface IRole {
    /** Name of the role */
    sName: string;
    /** Tag/identifier for the role */
    sTag: string;
    /** Reference to the head/parent role */
    tHead: string | Schema.Types.ObjectId | IUserInfo;
    /** Reference to the organization this role belongs to */
    tOrganization: string | Schema.Types.ObjectId | IOrganization;
}
interface IRoleModel extends IConcept, IRole {
}

interface IUser {
    id?: number;
    avatar?: string;
    image?: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    username?: string;
    email?: string;
    address?: IAddress;
    phone?: string;
}
interface IAddress {
    street: string;
    suite: string;
    city: string;
    zipcode: string;
}
interface IUserData extends Identifiable, IActiveStatus {
    sPassword: string;
    sEmail: string;
    aPhoneNumber: number;
    tBranches: any[];
    tRole: IRole;
    bCanLogin: boolean;
    bOnlyOfficePunch: boolean;
    tShift: IShift[];
    bIsApplicableAdvance: boolean;
    bIsResigned: boolean;
    bIsCreatedBySuperAdmin: boolean;
    bIsPermanentWFH: boolean;
    tIdEmployee: IEmployeeId;
    tUserDetails: IUserDetails;
    sProfileUrl?: string;
}
interface IEmployeeId extends IEmployeeCodeInfoDTO {
    tRole: string;
}
interface IEmployeeCodeInfoDTO extends Identifiable {
    sCode: string;
    aPunchId: number;
}

interface ILoginResponse {
    success: boolean;
    message: string;
    data: IUserData;
    headers: {
        [key: string]: string;
    };
}
interface IHttpResponse<T> {
    status: string;
    statusCode: number;
    data: T;
}

interface IDocument {
    /** Name of the document */
    sName: string;
    /** URL or path to the document */
    sUrl: string;
    /** Type of the document */
    eType: DocumentType;
    /** Size of the document in bytes */
    aSize: number;
    /** MIME type of the document */
    sMimeType: string;
    /** Upload date of the document */
    dUploadDate: Date;
    /** Reference to the uploader */
    tUploader: string | Types.ObjectId | IUserInfo;
}
interface IDocumentModel extends IConcept, IDocument {
}

interface IAction {
    icon?: string;
    label: string;
    action: () => void;
}
interface ILanguage {
    name: string;
    code: string;
}
interface IColumn {
    field: string;
    header: string;
}
interface IdentityInfo extends ITagType {
    title: string;
    icon?: string;
    tag?: string;
    noOfCols: number;
    values: IdentityInfoValue[];
}
interface IdentityInfoValue {
    label: string;
    value: string | number;
    valueHexColor?: string;
}
interface InfoCardInput {
    title: string;
    values: InfoCardValue[];
}
interface InfoCardValue {
    label: string;
    value: string | number;
    valueHexColor?: string;
}
interface ITagType {
    tagType?: 'success' | 'info' | 'warning' | 'danger' | 'primary' | 'secondary' | 'light' | 'dark';
}
interface IQuickLink {
    label: string;
    link: string;
}

interface AzadiAttendanceReport {
    aMonth: number;
    aTotalDays: number;
    aWeekends: number;
    aBusinessDays: number;
    aHolidays: number;
    aWorkingDays: number;
    aLeaves: number;
    aPresentDay: number;
}
interface AzadiEmployeeReport {
    aMonth: number;
    aNewJoin: number;
    aTotalLeft: number;
}

interface IDepartmentDTO extends IDepartment {
}

interface IDocumentDTO extends IDocument {
}

interface IOrganizationDTO extends IOrganization {
}

interface IRoleDTO extends IRole {
}

interface ITicketComment {
    id: number;
    comment: string;
    createdAt: Date;
    createdByUserId: number;
    ticket: ITicket;
}
interface ITicket extends ITicketBase, Identifiable {
}
interface ITicketBase {
    /** Reference to the organization */
    tOrganization: string | Schema.Types.ObjectId | IOrganization;
    /** User who requested the ticket */
    tRequester: string | Schema.Types.ObjectId | IUserInfo;
    /** Current status of the ticket */
    eStatusKey: TicketStatus;
    /** Category of the ticket */
    eCategoryKey: TicketCategory;
    /** Urgency level of the ticket */
    eUrgency: TicketUrgency;
    /** Impact category of the ticket */
    eImpactCategory: TicketImpactCategory;
    /** User impacted by the ticket */
    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];
    /** User who approved the ticket */
    tApprover: string | Schema.Types.ObjectId | IUserInfo;
    /** User assigned to handle the ticket */
    tAssignedTo: string | Schema.Types.ObjectId | IUserInfo;
    /** Observer of the ticket */
    tObserver: string | Schema.Types.ObjectId | IUserInfo;
    /** Associated asset with the ticket */
    tAsset: Record<string, any>;
    /** Description of the ticket */
    sDescription: string;
    /** Title of the ticket */
    sTitle: string;
    /** Incident date */
    dIncidentDate: Date;
    /** Array of attached documents */
    aDocuments: IDocument[];
}
interface ITicketModel extends IConcept, ITicketBase {
}
interface ITicketIdentifiable extends IConcept {
    tTicketId: string | Schema.Types.ObjectId | ITicket;
}
interface ITicketImpactModel extends ITicketIdentifiable {
    tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];
}
interface ITicketObserverModel extends ITicketIdentifiable {
    tObserver: string[] | Schema.Types.ObjectId[] | IUserInfo[];
}

interface IMessage {
    sender: IUser;
    content: string;
    date: Date;
    isRead?: boolean;
    receiver?: IUser;
}
interface INotification {
    subject: string;
    message: string;
}
interface IChatModel extends IConcept {
    tTicketId: string | ITicketModel;
    tSenderId: string | IUserInfoModel;
    sContent: string;
}

interface IUserInfoDTO extends Omit<IUserInfo, 'tRoleId' | 'tOrganizationId' | 'tDepartmentId' | 'tEmpCode' | 'sPassword'> {
    /** Role of the user (as string ID) */
    tRoleId?: string;
    /** Organization ID (as string) */
    tOrganizationId?: string;
    /** Department ID (as string) */
    tDepartmentId?: string;
    /** Password of the user (optional in DTO) */
    sPassword?: string;
    /** Employee Code (renamed in DTO) */
    empCode: string;
}

/**
 * DTO Interface for Ticket
 */
interface ITicketDTO extends Omit<ITicket, 'tRequester' | 'tImpactUser' | 'tApprover' | 'tAssignedTo' | 'tObserver' | 'aDocuments'> {
    /** User who requested the ticket */
    tRequester?: string | IUserInfoDTO;
    /** User impacted by the ticket */
    tImpactUsers?: string[] | IUserInfoDTO[];
    /** User who approved the ticket */
    tApprover?: string | IUserInfoDTO;
    /** User assigned to handle the ticket */
    tAssignedTo?: string | IUserInfoDTO;
    /** Observer(s) of the ticket */
    tObserver?: string[] | IUserInfoDTO[];
    /** Array of attached documents */
    aDocuments?: string[] | IDocumentDTO[];
}
interface IAllTicketDTO {
    tickets: ITicketDTO[];
    count: number;
}
interface ITicketReferenceDTO {
    /** Reference to the ticket */
    tTicketId: string | ITicketDTO;
}
interface ITicketObserverDTO extends ITicketReferenceDTO {
    /** Observers of the ticket */
    tObserver: string[] | IUserInfoDTO[];
}
interface ITicketImpactDTO extends ITicketReferenceDTO {
    /** Users impacted by the ticket */
    tImpactUser: string[] | IUserInfo[];
}
interface ICreateTicketTemplateDTO {
    tOrganization: string;
    tRequester: string;
    eStatusKey: string;
    eCategoryKey: string;
    eUrgency: string;
    eImpactCategory: TicketImpactCategory;
    tImpactUser: string;
    tApprover: string;
    tAssignedTo: string;
    tObserver: string[];
    tAsset: string;
    sDescription: string;
    sTitle: string;
    dIncidentDate: Date;
    aDocuments: string[];
}
interface ITicketTemplateDTO {
    department: string;
    searchText?: string;
    endDate?: string;
    startDate?: string;
    priority?: TicketSortPriority;
}
type TicketSortPriority = {
    _id?: 1 | -1;
    sTitle?: 1 | -1;
    eUrgency?: 1 | -1;
    dIncidentDate?: 1 | -1;
    'tRequester.sName'?: 1 | -1;
    'tAssignedTo.sName'?: 1 | -1;
};
interface ITicketStatusCountDto {
    total: number;
    open: number;
    closed: number;
}

declare const Ems_CONSTANT_DATA: {
    ADMIN: {
        QCM: {
            QUIZ_SUCCESS_MSG: string;
        };
    };
    USER: {
        QCM: {
            QUIZ_SUCCESS_MSG: string;
        };
    };
};

export { AdvanceStatus, AnnouncementType, AttendanceStatus, AttendanceType, CalendarEventType, DeductType, DocumentType, EmsAttendanceStatus, EmsAttendanceType, EmsUserLeaveStatus, EmsUserRequestStatus, Ems_CONSTANT_DATA, FileType, GovtSub, IncentiveType, InterviewMode, LeaveType, OrganizationSettingLabels, OrganizationSettingType, ProcessType, RequestStatus, RequestType, RoleRequestLabel, RoleSettingLabels, SalaryStructureCategory, SkillCategory, TicketCategory, TicketDepartment, TicketImpactCategory, TicketPriority, TicketStatus, TicketUrgency, UpdatedSalaryStructureCategory, UserAccess, emsSupportCategory, emsSupportPriority, emsSupportType };
export type { AzadiAttendanceReport, AzadiEmployeeReport, IAccess, IAction, IActiveStatus, IAllTicketDTO, IAnniversary, IAsset, IAttendanceReport, IAttendanceRequest, IBirthday, IChatModel, IColumn, IConcept, ICreateTicketTemplateDTO, IDayAttendance, IDepartment, IDepartmentDTO, IDepartmentModel, IDesignationInfoDTO, IDocument, IDocumentDTO, IDocumentModel, IEmployeeCodeInfoDTO, IEmployeeId, IHoliday, IHttpResponse, ILanguage, ILeaveBalance, ILeaveBalanceRequest, ILoginResponse, IMessage, INameEntity, INotification, IOrganization, IOrganizationBase, IOrganizationDTO, IOrganizationModel, IPost, IQuickLink, IRequest, IRole, IRoleDTO, IRoleModel, IShift, IStartDateEndDate, ITagEntity, ITagType, ITicket, ITicketComment, ITicketDTO, ITicketImpactDTO, ITicketImpactModel, ITicketModel, ITicketObserverDTO, ITicketObserverModel, ITicketReferenceDTO, ITicketStatusCountDto, ITicketTemplateDTO, ITimeStamp, ITimeStampIdentifiable, ITodaysAttendance, ITodaysAttendanceResponse, IUser, IUserData, IUserDetails, IUserInfo, IUserInfoDTO, IUserInfoModel, Identifiable, IdentityInfo, IdentityInfoValue, InfoCardInput, TicketSortPriority };
