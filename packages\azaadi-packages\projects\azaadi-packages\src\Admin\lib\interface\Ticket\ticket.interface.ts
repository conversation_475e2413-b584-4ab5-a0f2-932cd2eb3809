import { Schema } from 'mongoose';
import {
  TicketCategory,
  TicketImpactCategory,
  TicketStatus,
  TicketUrgency,
} from '../../enums';
import { IConcept, Identifiable } from '../Common';
import { IDocument } from '../Common/document.interface';
import { IOrganization } from '../Organization';
import { IUserInfo } from '../User';

export interface ITicketComment {
  id: number;
  comment: string;
  createdAt: Date;
  createdByUserId: number;
  ticket: ITicket;
}
export interface ITicket extends ITicketBase, Identifiable {}
interface ITicketBase {
  /** Reference to the organization */
  tOrganization: string | Schema.Types.ObjectId | IOrganization;

  /** User who requested the ticket */
  tRequester: string | Schema.Types.ObjectId | IUserInfo;

  /** Current status of the ticket */
  eStatusKey: TicketStatus;

  /** Category of the ticket */
  eCategoryKey: TicketCategory;

  /** Urgency level of the ticket */
  eUrgency: TicketUrgency;

  /** Impact category of the ticket */
  eImpactCategory: TicketImpactCategory;

  /** User impacted by the ticket */
  tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];

  /** User who approved the ticket */
  tApprover: string | Schema.Types.ObjectId | IUserInfo;

  /** User assigned to handle the ticket */
  tAssignedTo: string | Schema.Types.ObjectId | IUserInfo;

  /** Observer of the ticket */
  tObserver: string | Schema.Types.ObjectId | IUserInfo;

  /** Associated asset with the ticket */
  tAsset: Record<string, any>;

  /** Description of the ticket */
  sDescription: string;

  /** Title of the ticket */
  sTitle: string;

  /** Incident date */
  dIncidentDate: Date;

  /** Array of attached documents */
  aDocuments: IDocument[];
}

export interface ITicketModel extends IConcept, ITicketBase {}

interface ITicketIdentifiable extends IConcept {
  tTicketId: string | Schema.Types.ObjectId | ITicket;
}
export interface ITicketImpactModel extends ITicketIdentifiable {
  tImpactUser: string[] | Schema.Types.ObjectId[] | IUserInfo[];
}

export interface ITicketObserverModel extends ITicketIdentifiable {
  tObserver: string[] | Schema.Types.ObjectId[] | IUserInfo[];
}
