import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

interface PackageInfo {
  name: string;
  version: string;
  description: string;
  modules: string[];
}

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, CommonModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  title = 'azaadi-packages';

  packageInfo: PackageInfo = {
    name: 'azaadi-packages',
    version: '1.0.3',
    description:
      'A comprehensive Angular library package for Azaadi applications',
    modules: [
      'Admin Module',
      'Common Interfaces',
      'Dashboard Interfaces',
      'DTO Interfaces',
      'Organization Interfaces',
      'Ticket Interfaces',
      'User Interfaces',
      'Utility Constants',
      'Enums',
    ],
  };

  dependencies = [
    { name: 'mongoose', version: '^8.15.2', type: 'peer' },
    { name: 'tslib', version: '^2.3.0', type: 'dependency' },
  ];
}
