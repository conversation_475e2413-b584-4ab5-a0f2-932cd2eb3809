var AttendanceStatus;
(function (AttendanceStatus) {
    AttendanceStatus["PRESENT"] = "Present";
    AttendanceStatus["Leave"] = "Leave";
    AttendanceStatus["PUBLIC_HOLIDAY"] = "Public Holiday";
    AttendanceStatus["ORGANIZATION_WEEKEND"] = "Organization Weekend";
    AttendanceStatus["ERROR"] = "Error";
    AttendanceStatus["TODAYS_DATE"] = "Today's Date";
    AttendanceStatus["LHD"] = "Less Than Half Day";
    AttendanceStatus["HD"] = "Half Day";
})(AttendanceStatus || (AttendanceStatus = {}));
var AttendanceType;
(function (AttendanceType) {
    AttendanceType[AttendanceType["ONLINE"] = 0] = "ONLINE";
    AttendanceType[AttendanceType["OFFLINE"] = 1] = "OFFLINE";
})(AttendanceType || (AttendanceType = {}));

var UserAccess;
(function (UserAccess) {
    UserAccess[UserAccess["READ"] = 0] = "READ";
    UserAccess[UserAccess["WRITE"] = 1] = "WRITE";
    UserAccess[UserAccess["ALL"] = 2] = "ALL";
})(UserAccess || (UserAccess = {}));
var FileType;
(function (FileType) {
    FileType[FileType["pdf"] = 0] = "pdf";
    FileType[FileType["jpg"] = 1] = "jpg";
    FileType[FileType["jpeg"] = 2] = "jpeg";
    FileType[FileType["doc"] = 3] = "doc";
    FileType[FileType["excel"] = 4] = "excel";
    FileType[FileType["xml"] = 5] = "xml";
})(FileType || (FileType = {}));
var ProcessType;
(function (ProcessType) {
    ProcessType[ProcessType["EXAM"] = 0] = "EXAM";
    ProcessType[ProcessType["INTERVIEW"] = 1] = "INTERVIEW";
    ProcessType[ProcessType["DOCUMENT"] = 2] = "DOCUMENT";
    ProcessType[ProcessType["BACKGROUND"] = 3] = "BACKGROUND";
})(ProcessType || (ProcessType = {}));
var LeaveType;
(function (LeaveType) {
    LeaveType[LeaveType["EXAM"] = 0] = "EXAM";
    LeaveType[LeaveType["PERSONAL"] = 1] = "PERSONAL";
    LeaveType[LeaveType["SICK"] = 2] = "SICK";
    LeaveType[LeaveType["UNPAID"] = 3] = "UNPAID";
    LeaveType[LeaveType["OTHERS"] = 4] = "OTHERS";
})(LeaveType || (LeaveType = {}));
var CalendarEventType;
(function (CalendarEventType) {
    CalendarEventType[CalendarEventType["HOLIDAY"] = 0] = "HOLIDAY";
    CalendarEventType[CalendarEventType["WEEKOFF"] = 1] = "WEEKOFF";
    CalendarEventType[CalendarEventType["EVENT"] = 2] = "EVENT";
})(CalendarEventType || (CalendarEventType = {}));
var OrganizationSettingType;
(function (OrganizationSettingType) {
    OrganizationSettingType[OrganizationSettingType["SICK_LEAVE"] = 0] = "SICK_LEAVE";
    OrganizationSettingType[OrganizationSettingType["PERSONAL_LEAVE"] = 1] = "PERSONAL_LEAVE";
    OrganizationSettingType[OrganizationSettingType["EXAM_LEAVE"] = 2] = "EXAM_LEAVE";
    OrganizationSettingType[OrganizationSettingType["OTHER_LEAVE"] = 3] = "OTHER_LEAVE";
    OrganizationSettingType[OrganizationSettingType["UNPAID_LEAVE"] = 4] = "UNPAID_LEAVE";
    OrganizationSettingType[OrganizationSettingType["WORKING_HOUR"] = 5] = "WORKING_HOUR";
    OrganizationSettingType[OrganizationSettingType["DESCRIPTION"] = 6] = "DESCRIPTION";
    OrganizationSettingType[OrganizationSettingType["BRAND_NAME"] = 7] = "BRAND_NAME";
    OrganizationSettingType[OrganizationSettingType["LOGO"] = 8] = "LOGO";
    OrganizationSettingType[OrganizationSettingType["PHONE"] = 9] = "PHONE";
    OrganizationSettingType[OrganizationSettingType["ADDRESS"] = 10] = "ADDRESS";
    OrganizationSettingType[OrganizationSettingType["TOTAL_EMPLOYEE"] = 11] = "TOTAL_EMPLOYEE";
    OrganizationSettingType[OrganizationSettingType["HOLIDAY"] = 12] = "HOLIDAY";
    OrganizationSettingType[OrganizationSettingType["WEEKOFF"] = 13] = "WEEKOFF";
    OrganizationSettingType[OrganizationSettingType["SIGN_IN"] = 14] = "SIGN_IN";
    OrganizationSettingType[OrganizationSettingType["SIGN_OUT"] = 15] = "SIGN_OUT";
    OrganizationSettingType[OrganizationSettingType["SALARY_CIRCLE_START_DATE"] = 16] = "SALARY_CIRCLE_START_DATE";
    OrganizationSettingType[OrganizationSettingType["SALARY_CIRCLE_END_DATE"] = 17] = "SALARY_CIRCLE_END_DATE";
})(OrganizationSettingType || (OrganizationSettingType = {}));
var OrganizationSettingLabels;
(function (OrganizationSettingLabels) {
    OrganizationSettingLabels["Logo"] = "Logo";
    OrganizationSettingLabels["TotalAllowEmployee"] = "Total Allow Employee";
    OrganizationSettingLabels["SalaryDay"] = "Salary Day";
    OrganizationSettingLabels["Phone"] = "Phone No.";
    OrganizationSettingLabels["OfficialEmail"] = "Official Email";
    OrganizationSettingLabels["Website"] = "Website";
    OrganizationSettingLabels["Address"] = "Address";
    OrganizationSettingLabels["Description"] = "Description";
    OrganizationSettingLabels["IncentiveDay"] = "Incentive Day";
    OrganizationSettingLabels["TDSApplicable"] = "TDS Deduction Not Applicable";
})(OrganizationSettingLabels || (OrganizationSettingLabels = {}));
var RoleSettingLabels;
(function (RoleSettingLabels) {
    RoleSettingLabels["LateLogin"] = "Late Login (min)";
    RoleSettingLabels["EarlyLogout"] = "Early Logout (min)";
    RoleSettingLabels["PunchIn"] = "Punch In Time";
    RoleSettingLabels["PunchOut"] = "Punch Out Time";
    RoleSettingLabels["WeekOff"] = "Week Off";
    RoleSettingLabels["Holidays"] = "Calendar";
    RoleSettingLabels["isAdmin"] = "Admin?";
})(RoleSettingLabels || (RoleSettingLabels = {}));
var SalaryStructureCategory;
(function (SalaryStructureCategory) {
    SalaryStructureCategory[SalaryStructureCategory["NONPF"] = 0] = "NONPF";
    SalaryStructureCategory[SalaryStructureCategory["PF1"] = 1] = "PF1";
    SalaryStructureCategory[SalaryStructureCategory["PF2"] = 2] = "PF2";
})(SalaryStructureCategory || (SalaryStructureCategory = {}));
var UpdatedSalaryStructureCategory;
(function (UpdatedSalaryStructureCategory) {
    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory["NONPF"] = 0] = "NONPF";
    UpdatedSalaryStructureCategory[UpdatedSalaryStructureCategory["PF"] = 1] = "PF";
    // PFESI = 2,
})(UpdatedSalaryStructureCategory || (UpdatedSalaryStructureCategory = {}));
var GovtSub;
(function (GovtSub) {
    GovtSub[GovtSub["ABRY"] = 0] = "ABRY";
    GovtSub[GovtSub["NotApplicable"] = 1] = "NotApplicable";
})(GovtSub || (GovtSub = {}));
var SkillCategory;
(function (SkillCategory) {
    SkillCategory[SkillCategory["Unskilled"] = 0] = "Unskilled";
    SkillCategory[SkillCategory["SemiSkilled"] = 1] = "SemiSkilled";
    SkillCategory[SkillCategory["Skilled"] = 2] = "Skilled";
    SkillCategory[SkillCategory["HighlySkilled"] = 3] = "HighlySkilled";
    SkillCategory[SkillCategory["NotFollowed"] = 4] = "NotFollowed";
})(SkillCategory || (SkillCategory = {}));
var AnnouncementType;
(function (AnnouncementType) {
    AnnouncementType[AnnouncementType["Announcement"] = 0] = "Announcement";
    AnnouncementType[AnnouncementType["Birthday"] = 1] = "Birthday";
    AnnouncementType[AnnouncementType["WorkAnniversary"] = 2] = "WorkAnniversary";
    AnnouncementType[AnnouncementType["CalendarEvent"] = 3] = "CalendarEvent";
})(AnnouncementType || (AnnouncementType = {}));
var IncentiveType;
(function (IncentiveType) {
    IncentiveType["Monthly"] = "Monthly";
    IncentiveType["Quarterly"] = "Quarterly";
    IncentiveType["Yearly"] = "Yearly";
    IncentiveType["HalfYearly"] = "Half yearly";
})(IncentiveType || (IncentiveType = {}));
var DeductType;
(function (DeductType) {
    DeductType["Monthly"] = "Monthly";
    DeductType["Quarterly"] = "Quarterly";
    DeductType["HalfYearly"] = "Half Yearly";
    DeductType["Yearly"] = "Yearly";
})(DeductType || (DeductType = {}));
var AdvanceStatus;
(function (AdvanceStatus) {
    AdvanceStatus["Request"] = "Request";
    AdvanceStatus["Approved"] = "Approved";
    AdvanceStatus["Pending"] = "Pending";
    AdvanceStatus["HrApproved"] = "Approved By Hr";
    AdvanceStatus["Rejected"] = "Rejected";
    AdvanceStatus["Canceled"] = "Canceled";
})(AdvanceStatus || (AdvanceStatus = {}));

var RequestStatus;
(function (RequestStatus) {
    RequestStatus[RequestStatus["APPROVED"] = 0] = "APPROVED";
    RequestStatus[RequestStatus["PENDING"] = 1] = "PENDING";
    RequestStatus[RequestStatus["REJECTED"] = 2] = "REJECTED";
    RequestStatus[RequestStatus["UNPAID"] = 3] = "UNPAID";
    RequestStatus[RequestStatus["CANCELLED"] = 4] = "CANCELLED";
})(RequestStatus || (RequestStatus = {}));
var RequestType;
(function (RequestType) {
    RequestType[RequestType["SICK_LEAVE"] = 0] = "SICK_LEAVE";
    RequestType[RequestType["CASUAL_LEAVE"] = 1] = "CASUAL_LEAVE";
    RequestType[RequestType["WORK_FROM_HOME"] = 2] = "WORK_FROM_HOME";
})(RequestType || (RequestType = {}));
var RoleRequestLabel;
(function (RoleRequestLabel) {
    RoleRequestLabel["SICK_LEAVE"] = "Sick Leave";
    RoleRequestLabel["CASUAL_LEAVE"] = "Casual Leave";
    RoleRequestLabel["ANNUAL_LEAVE"] = "Annual Leave";
    RoleRequestLabel["MATERNITY_LEAVE"] = "Maternity Leave";
    RoleRequestLabel["PATERNITY_LEAVE"] = "Paternity Leave";
    RoleRequestLabel["BEREAVEMENT_LEAVE"] = "Bereavement Leave";
    RoleRequestLabel["WORK_FROM_HOME"] = "Work From Home";
})(RoleRequestLabel || (RoleRequestLabel = {}));

var emsSupportPriority;
(function (emsSupportPriority) {
    emsSupportPriority[emsSupportPriority["High"] = 0] = "High";
    emsSupportPriority[emsSupportPriority["Medium"] = 1] = "Medium";
    emsSupportPriority[emsSupportPriority["Low"] = 2] = "Low";
})(emsSupportPriority || (emsSupportPriority = {}));
var emsSupportType;
(function (emsSupportType) {
    emsSupportType[emsSupportType["Request"] = 0] = "Request";
    emsSupportType[emsSupportType["Incident"] = 1] = "Incident";
})(emsSupportType || (emsSupportType = {}));
var emsSupportCategory;
(function (emsSupportCategory) {
    emsSupportCategory[emsSupportCategory["Request"] = 0] = "Request";
})(emsSupportCategory || (emsSupportCategory = {}));

var TicketPriority;
(function (TicketPriority) {
    TicketPriority["LOW_PRIORITY"] = "LOW PRIORITY";
    TicketPriority["MEDIUM_PRIORITY"] = "MEDIUM PRIORITY";
    TicketPriority["HIGH_PRIORITY"] = "HIGH PRIORITY";
})(TicketPriority || (TicketPriority = {}));
var TicketDepartment;
(function (TicketDepartment) {
    TicketDepartment["IT_DEPARTMENT"] = "IT DEPARTMENT";
    TicketDepartment["HR_DEPARTMENT"] = "HR DEPARTMENT";
    TicketDepartment["FINANCE_DEPARTMENT"] = "FINANCE DEPARTMENT";
    TicketDepartment["DEV_DEPARTMENT"] = "DEV DEPARTMENT";
})(TicketDepartment || (TicketDepartment = {}));
var TicketStatus;
(function (TicketStatus) {
    TicketStatus["OPEN"] = "OPEN";
    TicketStatus["PICKED"] = "PICKED";
    TicketStatus["CLOSED"] = "CLOSED";
})(TicketStatus || (TicketStatus = {}));
var TicketCategory;
(function (TicketCategory) {
    TicketCategory["MANAGER"] = "MANAGER";
    TicketCategory["HR_MANAGEMENT"] = "HR_MANAGEMENT";
    TicketCategory["IT_SUPPORT"] = "IT_SUPPORT";
})(TicketCategory || (TicketCategory = {}));
var TicketUrgency;
(function (TicketUrgency) {
    TicketUrgency[TicketUrgency["LOW"] = 4] = "LOW";
    TicketUrgency[TicketUrgency["MEDIUM"] = 3] = "MEDIUM";
    TicketUrgency[TicketUrgency["HIGH"] = 2] = "HIGH";
    TicketUrgency[TicketUrgency["URGENT"] = 1] = "URGENT";
})(TicketUrgency || (TicketUrgency = {}));
var TicketImpactCategory;
(function (TicketImpactCategory) {
    TicketImpactCategory["INDIVIDUAL"] = "INDIVIDUAL";
    TicketImpactCategory["TEAM"] = "TEAM";
    TicketImpactCategory["DEPARTMENT"] = "DEPARTMENT";
})(TicketImpactCategory || (TicketImpactCategory = {}));
var DocumentType;
(function (DocumentType) {
    DocumentType["PDF"] = "PDF";
    DocumentType["IMAGE"] = "IMAGE";
    DocumentType["WORD"] = "WORD";
    DocumentType["EXCEL"] = "EXCEL";
    DocumentType["OTHER"] = "OTHER";
})(DocumentType || (DocumentType = {}));

var EmsAttendanceType;
(function (EmsAttendanceType) {
    EmsAttendanceType[EmsAttendanceType["ONLINE"] = 0] = "ONLINE";
    EmsAttendanceType[EmsAttendanceType["OFFLINE"] = 1] = "OFFLINE";
})(EmsAttendanceType || (EmsAttendanceType = {}));
var EmsUserLeaveStatus;
(function (EmsUserLeaveStatus) {
    EmsUserLeaveStatus[EmsUserLeaveStatus["APPROVED"] = 0] = "APPROVED";
    EmsUserLeaveStatus[EmsUserLeaveStatus["PENDING"] = 1] = "PENDING";
    EmsUserLeaveStatus[EmsUserLeaveStatus["REJECTED"] = 2] = "REJECTED";
})(EmsUserLeaveStatus || (EmsUserLeaveStatus = {}));
var EmsAttendanceStatus;
(function (EmsAttendanceStatus) {
    EmsAttendanceStatus[EmsAttendanceStatus["FULL_DAY"] = 0] = "FULL_DAY";
    EmsAttendanceStatus[EmsAttendanceStatus["HALF_DAY"] = 1] = "HALF_DAY";
    EmsAttendanceStatus[EmsAttendanceStatus["ERROR"] = 2] = "ERROR";
    EmsAttendanceStatus[EmsAttendanceStatus["ABSENT"] = 3] = "ABSENT";
    EmsAttendanceStatus[EmsAttendanceStatus["WEEK_OFF"] = 4] = "WEEK_OFF";
    EmsAttendanceStatus[EmsAttendanceStatus["LEAVE"] = 5] = "LEAVE";
    EmsAttendanceStatus[EmsAttendanceStatus["HOLIDAY"] = 6] = "HOLIDAY";
    EmsAttendanceStatus[EmsAttendanceStatus["LESS_THAN_HALF_DAY"] = 7] = "LESS_THAN_HALF_DAY";
})(EmsAttendanceStatus || (EmsAttendanceStatus = {}));
;
var EmsUserRequestStatus;
(function (EmsUserRequestStatus) {
    EmsUserRequestStatus[EmsUserRequestStatus["APPROVED"] = 0] = "APPROVED";
    EmsUserRequestStatus[EmsUserRequestStatus["PENDING"] = 1] = "PENDING";
    EmsUserRequestStatus[EmsUserRequestStatus["REJECTED"] = 2] = "REJECTED";
    EmsUserRequestStatus[EmsUserRequestStatus["UNPAID"] = 3] = "UNPAID";
    EmsUserRequestStatus[EmsUserRequestStatus["CANCEL"] = 4] = "CANCEL";
})(EmsUserRequestStatus || (EmsUserRequestStatus = {}));
;

// export interface IUserListDTO extends IEmployeeCodeInfoDTO {
//   sEmail: string;
//   sCode: string;
//   aPhoneNumber: number;
//   sName: string;
//   sProfileUrl?: string;
//   tOrganization: IOrganization;
//   dJoiningDate: Date;
//   tRole: IRole;
//   bIsActive: boolean;
// }

const Ems_CONSTANT_DATA = {
    ADMIN: {
        QCM: {
            QUIZ_SUCCESS_MSG: 'fdfd dfdf',
        },
    },
    USER: {
        QCM: {
            QUIZ_SUCCESS_MSG: 'fdfd dfdf',
        },
    },
};

/**
 * Generated bundle index. Do not edit.
 */

export { AdvanceStatus, AnnouncementType, AttendanceStatus, AttendanceType, CalendarEventType, DeductType, DocumentType, EmsAttendanceStatus, EmsAttendanceType, EmsUserLeaveStatus, EmsUserRequestStatus, Ems_CONSTANT_DATA, FileType, GovtSub, IncentiveType, LeaveType, OrganizationSettingLabels, OrganizationSettingType, ProcessType, RequestStatus, RequestType, RoleRequestLabel, RoleSettingLabels, SalaryStructureCategory, SkillCategory, TicketCategory, TicketDepartment, TicketImpactCategory, TicketPriority, TicketStatus, TicketUrgency, UpdatedSalaryStructureCategory, UserAccess, emsSupportCategory, emsSupportPriority, emsSupportType };
//# sourceMappingURL=azaadi-packages.mjs.map
