import { Document, SchemaTimestampsConfig, Types } from 'mongoose';
export interface Identifiable {
  _id: string | Types.ObjectId;
}

export interface ITimeStamp {
  createdAt?: Date;
  updatedAt?: Date;
}
export interface INameEntity extends Identifiable {
  sName: string;
}
export interface ITagEntity extends INameEntity {
  sTag?: string;
}
export interface ITimeStampIdentifiable extends ITimeStamp, Identifiable {}

export interface IActiveStatus {
  bIsActive: boolean;
}
export interface IStartDateEndDate {
  dStartDate: Date;
  dEndDate: Date;
}
export interface IConcept extends Document, SchemaTimestampsConfig {}
