# Azaadi Packages

This project contains the Azaadi Angular library package and a demo application.

## Quick Start

### Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

### Build Library Package

```bash
# Build and create .tgz file locally
npm run pack-azaadi

# Build and upload to Google Drive (requires setup)
npm run build-and-upload
```

## Google Drive Auto-Upload Setup

The project includes automatic Google Drive upload functionality for built packages.

### Setup Google Drive Upload

```bash
# Interactive setup wizard
npm run setup-google-drive

# Validate configuration
npm run validate-config
```

See [GOOGLE_DRIVE_SETUP.md](./GOOGLE_DRIVE_SETUP.md) for detailed setup instructions.

## Available Scripts

| Command                      | Description                              |
| ---------------------------- | ---------------------------------------- |
| `npm start`                  | Start development server                 |
| `npm run build`              | Build the application                    |
| `npm run pack-azaadi`        | Build library and create .tgz file       |
| `npm run build-and-upload`   | Build library and upload to Google Drive |
| `npm run setup-google-drive` | Configure Google Drive upload            |
| `npm run validate-config`    | Check Google Drive configuration         |
| `npm test`                   | Run unit tests                           |

## Package Information

- **Name**: azaadi-packages
- **Version**: 1.0.3
- **Type**: Angular Library
- **Output**: `.tgz` file for distribution

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.

## Repository

https://github.com/bashaquerizwan/azaadi.git
