import { IConcept, Identifiable } from '../Common/common.interface';
export interface IOrganizationBase {
  /** Tag/identifier for the organization */
  sTag: string;

  /** Name of the organization */
  sName: string;

  /** Description of the organization */
  sDescription: string;

  /** Branch /company name */
  sBrandName: string;

  /** Logo URL or path */
  sLogo: string;

  /** Organization's email address */
  sEmail: string;
}
export interface IOrganization extends IOrganizationBase, Identifiable {}
export interface IOrganizationModel extends IConcept, IOrganizationBase {}
